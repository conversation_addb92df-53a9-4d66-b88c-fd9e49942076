package main

import (
	"context"
	"fmt"
	"net/http"
	"net/netip"
	"strings"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/activitylogs"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/aws"
	awsScheduler "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/scheduler/aws"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ec2"

	// Azure deps
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute"
	azureprov "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/azure"
	cloudstate "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/scheduler/cloudinstance"
)

func addNodeTypeCloudInstanceRoutes(api huma.API, a *application) {
	// Get Node Type Cloud Instance
	type GetNodeCloudInstanceInput struct {
		NodeID              string `path:"nodeID" format:"uuid" doc:"Node ID" example:"a58a3afc-c34e-440d-ba75-2045bb0c7577"`
		IncludeActivityLogs bool   `query:"activity_logs" doc:"Whether to include activity logs (optional). Example: true or false." default:"true"`
	}

	type NodeCloudInstance struct {
		Provider               string      `json:"provider" enum:"AWS,GCP,AZURE"`
		Region                 string      `json:"region" example:"eu-west-2"`
		OperatingSystemImageID string      `json:"operating_system_image_id"`
		InstanceType           string      `json:"instance_type" doc:"AWS Instance type" example:"t2.micro"`
		Name                   string      `json:"name" doc:"Instance name" pattern:"^[a-zA-Z0-9 _.:/=+\\-@]{1,256}$" minLength:"1" maxLength:"256"`
		PublicIpV4Address      *netip.Addr `json:"public_ipv4_address" format:"ipv4"`
		OpenIngressTcpPorts    []int32     `json:"open_ingress_tcp_ports" doc:"Open ingress TCP ports" example:"[22]"`
		NodeID                 string      `json:"node_id" format:"uuid" doc:"Node ID"`
		CideploymentStatus     string      `json:"ci_deployment_status" enum:"PENDING,IN-PROGRESS,SUCCESS,WARNING,ERROR"`
		CloudInstanceState     string      `json:"cloud_instance_state" enum:"pending,running,stopping,stopped,shutting-down,terminated,error,new"`
		CloudInstanceID        string      `json:"cloud_instance_id" example:"i-034295fe21c3bebf7"`
	}

	type GetNodeCloudInstanceOutput struct {
		Body struct {
			Node         NodeCloudInstance `json:"node"`
			ActivityLogs []ActivityLog     `json:"activity_logs,omitempty" doc:"Activity Logs"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-nodes-cloud-instance",
		Method:        http.MethodGet,
		Path:          "/nodes/cloud_instance/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get a Cloud Instance Node",
		Tags:          []string{"Nodes, Cloud Instance"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *GetNodeCloudInstanceInput) (*GetNodeCloudInstanceOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetNodeCloudInstanceOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, err
		}
		node, err := a.queries.GetNodeTypeCloudInstance(context.Background(), db.GetNodeTypeCloudInstanceParams{
			NodeID: *nodeIDPgType,
			ID:     *userIDPgType,
		})
		if err != nil {
			a.logger.Error("Error getting Node Cloud Instance", "nodeID", i.NodeID, "error", err)
			return nil, huma.Error404NotFound("Node not found")
		}

		resp.Body.Node.Provider = string(node.Provider)
		resp.Body.Node.Region = node.Region
		resp.Body.Node.OperatingSystemImageID = node.OperatingSystemImageID
		resp.Body.Node.InstanceType = node.InstanceSize
		resp.Body.Node.Name = node.InstanceName
		resp.Body.Node.PublicIpV4Address = node.PublicIpv4Address
		resp.Body.Node.OpenIngressTcpPorts = node.OpenPorts
		resp.Body.Node.NodeID = i.NodeID
		resp.Body.Node.CideploymentStatus = string(node.CiDeploymentStatus)
		resp.Body.Node.CloudInstanceState = string(node.CloudInstanceState.CiStateEnum)
		resp.Body.Node.CloudInstanceID = node.CloudInstanceID.String

		if i.IncludeActivityLogs {
			activityLogs := make([]ActivityLog, 0)
			activityLogsDB, err := a.queries.GetNodeActivityLogs(context.Background(), *nodeIDPgType)
			if err != nil {
				a.logger.Error("Error getting activity logs", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
				return nil, huma.Error404NotFound("Activity logs not found")
			}
			for _, activityLogDB := range activityLogsDB {
				activityLogs = append(activityLogs, ActivityLog{
					Message:   activityLogDB.Message,
					Type:      activityLogDB.Type,
					Username:  activityLogDB.Username,
					CreatedAt: activityLogDB.CreatedAt.Time,
				})
			}
			resp.Body.ActivityLogs = activityLogs
		}
		return resp, nil
	})

	// Edit Node Type Cloud Instance
	type EditNodeTypeCloudInstanceInput struct {
		NodeID string `path:"nodeID" format:"uuid" doc:"Node ID" example:"a58a3afc-c34e-440d-ba75-2045bb0c7577"`
		Body   struct {
			Name                string  `json:"name"`
			OpenIngressTcpPorts []int32 `json:"open_ingress_tcp_ports"`
		}
	}

	type EditNodeTypeCloudInstanceOutput struct {
		Body struct {
			Provider               string      `json:"provider" enum:"AWS,GCP,AZURE"`
			Region                 string      `json:"region" example:"eu-west-2"`
			OperatingSystemImageID string      `json:"operating_system_image_id"`
			InstanceType           string      `json:"instance_type" doc:"AWS Instance type" example:"t2.micro"`
			Name                   string      `json:"name" doc:"Instance name" pattern:"^[a-zA-Z0-9 _.:/=+\\-@]{1,256}$" minLength:"1" maxLength:"256"`
			PublicIpV4Address      *netip.Addr `json:"public_ipv4_address" format:"ipv4"`
			OpenIngressTcpPorts    []int32     `json:"open_ingress_tcp_ports" doc:"Open ingress TCP ports" example:"[22]"`
			NodeID                 string      `json:"node_id" format:"uuid"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-nodes-cloud-instance",
		Method:        http.MethodPut,
		Path:          "/nodes/cloud_instance/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit a Cloud Instance Node",
		Tags:          []string{"Nodes, Cloud Instance"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *EditNodeTypeCloudInstanceInput) (*EditNodeTypeCloudInstanceOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &EditNodeTypeCloudInstanceOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		updatedNodeCloudInstance, err := a.queries.UpdateNodeTypeCloudInstance(context.Background(),
			db.UpdateNodeTypeCloudInstanceParams{
				NodeID:    *nodeIDPgType,
				Name:      i.Body.Name,
				OpenPorts: i.Body.OpenIngressTcpPorts,
			})
		if err != nil {
			a.logger.Error("Error updating CloudInstance Node in database", "nodeID", i.NodeID, "error", err)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Update the nodes table with the name
		err = a.queries.UpdateNodeName(ctx, db.UpdateNodeNameParams{
			Name: i.Body.Name,
			ID:   *nodeIDPgType,
		})
		if err != nil {
			a.logger.Error("Error updating Node name in database", "nodeID", i.NodeID, "error", err)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Updated successfully", db.LogsNodesTypeEnumNODEUPDATE, *userIDPgType, *nodeIDPgType, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// TODO potential code for editing an instance with Terraform?
		//node, err := a.queries.GetNodeTypeCloudInstance(context.Background(), db.GetNodeTypeCloudInstanceParams{
		//	NodeID: *nodeIDPgType,
		//	ID:     *userIDPgType,
		//})
		//if err != nil {
		//	return nil, huma.Error500InternalServerError("Something went wrong")
		//}
		//
		//awsInstanceDeployment, _ := deployments.NewAwsCloudInstanceDeployment(
		//	a.queries,
		//	a.rabbitMqChannel,
		//	*userIDPgType,
		//	*nodeIDPgType,
		//	node.EngagementID,
		//	updatedNodeCloudInstance.Region,
		//	updatedNodeCloudInstance.OperatingSystemImageID,
		//	updatedNodeCloudInstance.Size,
		//	updatedNodeCloudInstance.Name,
		//	updatedNodeCloudInstance.OpenPorts)
		//
		//err = awsInstanceDeployment.WriteTemplate()
		//if err != nil {
		//	a.logger.Error("Error generating Terraform template", "error", err.Error())
		//	return nil, huma.Error500InternalServerError("Something went wrong")
		//}

		a.logger.Info("Updated CloudInstance Node successfully", "node_id", i.NodeID)
		resp.Body.NodeID = i.NodeID
		resp.Body.Name = updatedNodeCloudInstance.Name
		resp.Body.Region = updatedNodeCloudInstance.Region
		resp.Body.Provider = string(updatedNodeCloudInstance.Provider)
		resp.Body.OpenIngressTcpPorts = updatedNodeCloudInstance.OpenPorts
		resp.Body.OperatingSystemImageID = updatedNodeCloudInstance.OperatingSystemImageID
		resp.Body.InstanceType = updatedNodeCloudInstance.InstanceType

		return resp, nil
	})

	// Create Node Cloud Instance
	// AWS tag requirements: https://docs.aws.amazon.com/pdfs/tag-editor/latest/userguide/tag-editor-userguide.pdf

	type OperatingSystemImageAzure struct {
		Publisher string `json:"publisher,omitempty"`
		Offer     string `json:"offer,omitempty"`
		SKU       string `json:"sku,omitempty"`
		Version   string `json:"version,omitempty"`
	}
	type NodeCloudInstanceInput struct {
		Body struct {
			EngagementID           string                     `json:"engagement_id" doc:"Engagement ID" format:"uuid" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
			NodeGroupID            string                     `json:"node_group_id,omitempty" format:"uuid" doc:"Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
			Provider               string                     `json:"provider" enum:"AWS,GCP,AZURE"`
			Region                 string                     `json:"region" example:"eu-west-2"`
			OperatingSystemImageID string                     `json:"operating_system_image_id,omitempty"` // for AWS
			OperatingSystemImage   *OperatingSystemImageAzure `json:"operating_system_image,omitempty"`    // for Azure
			InstanceType           string                     `json:"instance_type" doc:"AWS Instance type" example:"t2.micro"`
			Name                   string                     `json:"name" doc:"Instance name" pattern:"^[a-zA-Z0-9 _.:/=+\\-@]{1,256}$" minLength:"1" maxLength:"256"`
			OpenIngressTcpPorts    []int32                    `json:"open_ingress_tcp_ports" doc:"Open ingress TCP ports" example:"[22]"`
			StartupScript          string                     `json:"startup_script,omitempty" doc:"Startup script in base64 encoding" example:"c3VkbyBhcHQgdXBkYXRlIC15ICYmIHN1ZG8gYXB0IGluc3RhbGwgLXkgcHl0aG9uMyBweXRob24zLXBpcA=="`
			SelectedAccountID      string                     `json:"selected_account_id"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-cloud-instance",
		Method:        http.MethodPost,
		Path:          "/nodes/cloudinstance",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create a Cloud Instance Node for an Engagement",
		Tags:          []string{"Nodes, Cloud Instance"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *NodeCloudInstanceInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		engagementIDPgType, err := converters.StringToPgTypeUUID(i.Body.EngagementID)
		if err != nil {
			a.logger.Error("Error parsing EngagementID", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		var associatedNodeGroupID pgtype.UUID

		// No Node Group ID was provided to create the Node in, create a new one
		if len(i.Body.NodeGroupID) == 0 {
			associatedNodeGroup, err := a.queries.CreateNodeGroup(context.Background(), db.CreateNodeGroupParams{
				Name:         "New Node Group",
				IsActive:     true,
				EngagementID: *engagementIDPgType,
				CreatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
				UpdatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
			})
			if err != nil {
				a.logger.Error("Error creating new Node Group in database", "error", err.Error(),
					"user_id", userID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "cloud_instance",
					"node_group", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			associatedNodeGroupID = associatedNodeGroup.ID
		} else {
			// An existing Node Group ID was provided, check if it is associated with the Engagement
			// and associate it with the new Node
			nodeGroupIDPgType, err := converters.StringToPgTypeUUID(i.Body.NodeGroupID)
			if err != nil {
				a.logger.Error("Error parsing NodeGroupID", "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			nodeGroupsDB, err := a.queries.GetEngagementNodeGroup(context.Background(), db.GetEngagementNodeGroupParams{
				ID:           *nodeGroupIDPgType,
				EngagementID: *engagementIDPgType,
			})
			if err != nil {
				a.logger.Error("Error getting Node Groups", "error", err.Error(), "node_group_id", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			// If the Node Group is associated with the Engagement, use its ID
			// Otherwise return a generic error to the user for security reasons
			if len(nodeGroupsDB) == 1 {
				associatedNodeGroupID = nodeGroupsDB[0].ID
			} else {
				a.logger.Error("Error getting Node Group associated with Engagement while creating a Node",
					"user_id", userID,
					"node_group_id", i.Body.NodeGroupID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "cloud_instance")
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		createdNode, err := a.queries.CreateNode(context.Background(), db.CreateNodeParams{
			NodeType:    "CLOUD_INSTANCE",
			Name:        i.Body.Name,
			NodeGroupID: associatedNodeGroupID,
		})
		if err != nil {
			a.logger.Error("Error creating Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "cloud_instance",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		params := db.CreateCloudInstanceNodeParams{
			Provider:           db.ProviderEnum(i.Body.Provider),
			Region:             i.Body.Region,
			InstanceType:       i.Body.InstanceType,
			Name:               i.Body.Name,
			NodeID:             createdNode.ID,
			OpenPorts:          i.Body.OpenIngressTcpPorts,
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnumNew, Valid: true},
		}

		// cloud provider dependent params adding
		accountUUID, err := converters.StringToPgTypeUUID(i.Body.SelectedAccountID)
		if err != nil {
			a.logger.Error("Invalid Cloud Account ID", "err", err)
			return nil, huma.Error400BadRequest("Invalid Cloud Account ID")
		}

		switch i.Body.Provider {
		case "AWS":
			params.AwsAccountID = *accountUUID
			params.OperatingSystemImageID = i.Body.OperatingSystemImageID

		case "AZURE":
			params.AzureTenantID = *accountUUID
			img := i.Body.OperatingSystemImage
			params.OperatingSystemImageID = fmt.Sprintf("%s:%s:%s:%s", img.Publisher, img.Offer, img.SKU, img.Version)

		default:
			return nil, huma.Error400BadRequest("Unsupported provider")
		}

		err = a.queries.CreateCloudInstanceNode(context.Background(), params)

		if err != nil {
			a.logger.Error("Error creating Cloud Instance Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "cloud_instance",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Created successfully", db.LogsNodesTypeEnumNODECREATION, *userIDPgType, createdNode.ID, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		switch i.Body.Provider {
		case "AWS":
			sshPublicKey, err := a.queries.GetAccountSshPublicKey(ctx, *accountUUID)

			if err != nil {
				a.logger.Error("Failed to retrieve public SSH key for Account", "user_id", userID, "error", err)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			ch, err := a.getChannel()
			if err != nil {
				return nil, huma.Error400BadRequest("Failed to get RabbitMQ channel", err)
			}

			awsCloudInstanceDeployment, _ := deployments.NewAwsCloudInstanceDeployment(
				a.queries,
				ch,
				*userIDPgType,
				createdNode.ID,
				*engagementIDPgType,
				i.Body.Region,
				i.Body.OperatingSystemImageID,
				i.Body.InstanceType,
				i.Body.Name,
				i.Body.OpenIngressTcpPorts,
				i.Body.StartupScript,
				sshPublicKey,
				a.awsRootRegion,
				i.Body.SelectedAccountID)

			err = awsCloudInstanceDeployment.WriteTemplate()
			if err != nil {
				a.logger.Error("Error generating Terraform template", "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

		case "AZURE":
			sshPublicKey, err := a.queries.GetAzureSshPublicKey(ctx, *accountUUID)

			if err != nil {
				a.logger.Error("Failed to retrieve public SSH key for Account", "user_id", userID, "error", err)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			ch, err := a.getChannel()
			if err != nil {
				return nil, huma.Error400BadRequest("Failed to get RabbitMQ channel", err)
			}

			azureCloudInstanceDeployment, _ := deployments.NewAzureCloudInstanceDeployment(
				a.queries,
				ch,
				*userIDPgType,
				createdNode.ID,
				*engagementIDPgType,
				i.Body.Region,
				i.Body.OperatingSystemImage.Publisher,
				i.Body.OperatingSystemImage.Offer,
				i.Body.OperatingSystemImage.SKU,
				i.Body.OperatingSystemImage.Version,
				i.Body.InstanceType,
				i.Body.Name,
				i.Body.OpenIngressTcpPorts,
				i.Body.StartupScript,
				a.awsRootRegion,
				sshPublicKey,
				i.Body.SelectedAccountID,
			)
			err = azureCloudInstanceDeployment.WriteTemplate()
			if err != nil {
				a.logger.Error("Error generating Terraform template", "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		default:
			a.logger.Error("Unsupported provider", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		createdNodeIDString, err := converters.PgTypeUUIDToString(createdNode.ID)
		if err != nil {
			a.logger.Error("Error converting Node ID to string", "error", err.Error(), "node_type", createdNode.NodeType, "node_id", createdNode.ID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		a.logger.Info("Created Cloud Instance Node successfully",
			"user_id", userID,
			"engagement_id", i.Body.EngagementID,
			"provider", params.Provider,
			"region", i.Body.Region,
			"operating_system_image_id", params.OperatingSystemImageID,
			"size", i.Body.InstanceType,
			"name", i.Body.Name,
			"open_ingress_tcp_ports", i.Body.OpenIngressTcpPorts,
			"startup_script", i.Body.StartupScript,
			"node_id", createdNodeIDString,
			"selected_account_ID", i.Body.SelectedAccountID)
		return nil, nil
	})

	// Get instance types with priorities first from the instance_size_mappings table and check availability in the region
	type GetInstanceTypesDBOutput struct {
		Body struct {
			InstanceTypes []struct {
				Alias string `json:"alias"`
				Type  string `json:"type"`
			} `json:"instance_types"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-instance-types",
		Method:        http.MethodGet,
		Path:          "/nodes/cloud_instance/instance-types/{region}/{amiId}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get instance types from the database",
		Tags:          []string{"Nodes, Cloud Instance, Instances"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		Region string `path:"region" maxLength:"20" example:"eu-west-2" doc:"AWS Region"`
		AmiId  string `path:"amiId" example:"ami-0123456789abcdef0" doc:"AMI ID to filter compatible instance types"`
	}) (*GetInstanceTypesDBOutput, error) {

		// Create AWS EC2 client
		awsEc2Client := aws.NewEc2Client(i.Region)

		// Get instance types
		instanceTypes, err := awsEc2Client.GetInstanceTypes(a.queries, i.AmiId)
		if err != nil {
			a.logger.Error("Error getting instance types", "error", err)
			return nil, huma.Error500InternalServerError("Failed to get instance types")
		}

		// If no instance types are found in the database, fallback to AWS API
		if len(instanceTypes) == 0 {
			a.logger.Warn("No instance types found in database, falling back to AWS API", "region", i.Region, "amiId", i.AmiId)

			instanceTypes, err = awsEc2Client.GetInstanceTypes(a.queries, i.AmiId)
			if err != nil {
				a.logger.Error("Error getting instance types from AWS API", "error", err)
				return nil, huma.Error500InternalServerError("Failed to get instance types from AWS API")
			}
		}

		// Prepare response
		resp := &GetInstanceTypesDBOutput{}
		resp.Body.InstanceTypes = make([]struct {
			Alias string `json:"alias"`
			Type  string `json:"type"`
		}, len(instanceTypes))

		for i, instanceType := range instanceTypes {
			resp.Body.InstanceTypes[i] = struct {
				Alias string `json:"alias"`
				Type  string `json:"type"`
			}{
				Alias: instanceType.Alias,
				Type:  instanceType.Type,
			}
		}

		return resp, nil
	})

	// ===== AWS instance lifecycle actions =====
	type awsActionOutput struct {
		Body struct {
			Message string `json:"message"`
			State   string `json:"state,omitempty"`
		} `json:"body"`
	}

	getAwsClientForNode := func(ctx context.Context, nodeID string) (*ec2.Client, string, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			a.logger.Error("getAwsClientForNode: unauthorized - no userID in context")
			return nil, "", fmt.Errorf("unauthorized")
		}
		userIDPg, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			a.logger.Error("getAwsClientForNode: invalid userID", "userID", userID, "error", err)
			return nil, "", err
		}
		nodeIDPg, err := converters.StringToPgTypeUUID(nodeID)
		if err != nil {
			a.logger.Error("getAwsClientForNode: invalid nodeID", "nodeID", nodeID, "error", err)
			return nil, "", err
		}
		a.logger.Info("getAwsClientForNode: querying node", "nodeID", nodeID, "userID", userID)
		node, err := a.queries.GetNodeTypeCloudInstance(context.Background(), db.GetNodeTypeCloudInstanceParams{NodeID: *nodeIDPg, ID: *userIDPg})
		if err != nil {
			a.logger.Error("getAwsClientForNode: failed to get node", "nodeID", nodeID, "userID", userID, "error", err)
			return nil, "", err
		}
		if string(node.Provider) != "AWS" {
			a.logger.Error("getAwsClientForNode: provider is not AWS", "provider", string(node.Provider), "nodeID", nodeID)
			return nil, "", fmt.Errorf("provider is not AWS")
		}
		if !node.CloudInstanceID.Valid || node.CloudInstanceID.String == "" {
			a.logger.Error("getAwsClientForNode: cloud_instance_id is empty", "nodeID", nodeID, "valid", node.CloudInstanceID.Valid, "value", node.CloudInstanceID.String)
			return nil, "", fmt.Errorf("cloud_instance_id is empty")
		}
		// Find AWS account ID for this node via engagement instances
		a.logger.Info("getAwsClientForNode: querying engagement instances", "engagementID", node.EngagementID)
		instances, err := a.queries.GetEngagementCloudInstances(context.Background(), node.EngagementID)
		if err != nil {
			a.logger.Error("getAwsClientForNode: failed to get engagement instances", "engagementID", node.EngagementID, "error", err)
			return nil, "", err
		}
		a.logger.Info("getAwsClientForNode: found instances", "count", len(instances), "nodeID", node.NodeID)
		var awsAccountID pgtype.UUID
		for _, ci := range instances {
			if ci.NodeID == node.NodeID {
				awsAccountID = ci.AwsAccountID
				a.logger.Info("getAwsClientForNode: found matching node", "nodeID", node.NodeID, "awsAccountID", ci.AwsAccountID)
				break
			}
		}
		if !awsAccountID.Valid {
			a.logger.Error("getAwsClientForNode: could not resolve AWS account for node", "nodeID", node.NodeID, "engagementID", node.EngagementID)
			return nil, "", fmt.Errorf("could not resolve AWS account for node")
		}
		accountIDStr, err := converters.PgTypeUUIDToString(awsAccountID)
		if err != nil {
			a.logger.Error("getAwsClientForNode: invalid aws account id", "awsAccountID", awsAccountID, "error", err)
			return nil, "", fmt.Errorf("invalid aws account id: %w", err)
		}
		// Fetch sub-account AWS credentials from Secrets Manager
		a.logger.Info("getAwsClientForNode: creating secrets manager", "awsRootRegion", a.awsRootRegion)
		sm, err := keys.NewSecretsManager(a.awsRootRegion)
		if err != nil {
			a.logger.Error("getAwsClientForNode: failed to create secrets manager", "awsRootRegion", a.awsRootRegion, "error", err)
			return nil, "", err
		}
		a.logger.Info("getAwsClientForNode: fetching secret", "accountID", *accountIDStr)
		secretMap, err := sm.GetSecret(*accountIDStr)
		if err != nil {
			a.logger.Error("getAwsClientForNode: failed to get secret", "accountID", *accountIDStr, "error", err)
			return nil, "", err
		}
		accessKey := secretMap["access_key_id"]
		secretKey := secretMap["access_key_secret"]
		a.logger.Info("getAwsClientForNode: retrieved credentials", "accountID", *accountIDStr, "accessKeyLength", len(accessKey), "secretKeyLength", len(secretKey))

		if accessKey == "" {
			a.logger.Error("getAwsClientForNode: access_key_id is empty", "accountID", *accountIDStr)
			return nil, "", fmt.Errorf("access_key_id is empty for account %s", *accountIDStr)
		}
		if secretKey == "" {
			a.logger.Error("getAwsClientForNode: access_key_secret is empty", "accountID", *accountIDStr)
			return nil, "", fmt.Errorf("access_key_secret is empty for account %s", *accountIDStr)
		}

		a.logger.Info("getAwsClientForNode: creating AWS config", "region", node.Region)
		cfg, err := config.LoadDefaultConfig(context.TODO(),
			config.WithRegion(node.Region),
			config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, secretKey, "")),
		)
		if err != nil {
			a.logger.Error("getAwsClientForNode: failed to load AWS config", "region", node.Region, "error", err)
			return nil, "", err
		}
		client := ec2.NewFromConfig(cfg)
		a.logger.Info("getAwsClientForNode: successfully created AWS client", "nodeID", nodeID, "instanceID", node.CloudInstanceID.String)
		return client, node.CloudInstanceID.String, nil
	}

	// Resolve Azure VM client + instanceID for node
	getAzureClientForNode := func(ctx context.Context, nodeID string) (*armcompute.VirtualMachinesClient, string, error) {
		nodeIDPg, err := converters.StringToPgTypeUUID(nodeID)
		if err != nil {
			return nil, "", err
		}
		userID, _ := ctx.Value("userID").(string)
		userIDPg, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, "", err
		}
		node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{NodeID: *nodeIDPg, ID: *userIDPg})
		if err != nil {
			return nil, "", err
		}
		if node.Provider != "AZURE" {
			return nil, "", fmt.Errorf("node is not Azure provider")
		}
		// node.CloudInstanceID holds full resource ID. AzureTenantID is on engagement instances list.
		instances, err := a.queries.GetEngagementCloudInstances(ctx, node.EngagementID)
		if err != nil {
			return nil, "", err
		}
		var tenantID string
		var subscriptionID string
		for _, ci := range instances {
			if ci.NodeID == node.NodeID {
				if ci.AzureTenantID.Valid {
					tid, _ := converters.PgTypeUUIDToString(ci.AzureTenantID)
					tenantID = *tid
				}
				// SubscriptionID might not be on this list; if not, parse from resource ID
				break
			}
		}
		if tenantID == "" {
			return nil, "", fmt.Errorf("could not resolve Azure tenant for node")
		}
		secret, err := azureprov.GetAzureSecret(tenantID)
		if err != nil {
			return nil, "", err
		}
		cred, err := azidentity.NewClientSecretCredential(secret.TenantID, secret.AppID, secret.AppSecret, nil)
		if err != nil {
			return nil, "", err
		}
		// Try to parse subscription from resource ID
		parts := strings.Split(node.CloudInstanceID.String, "/")
		if len(parts) >= 3 {
			subscriptionID = parts[2]
		}
		if subscriptionID == "" {
			// fallback to secret subscription
			subscriptionID = secret.SubscriptionID
		}
		client, err := armcompute.NewVirtualMachinesClient(subscriptionID, cred, nil)
		if err != nil {
			return nil, "", err
		}
		return client, node.CloudInstanceID.String, nil
	}

	// ===== Azure instance lifecycle actions =====
	// Azure action output type (reusing awsActionOutput shape for simplicity)
	azureStartStop := func(ctx context.Context, nodeID string, verb string) (*awsActionOutput, error) {
		client, instanceID, err := getAzureClientForNode(ctx, nodeID)
		if err != nil {
			return nil, huma.Error400BadRequest("Failed to prepare Azure client", err)
		}
		// Extract resource group and vm name from resource ID
		parts := strings.Split(instanceID, "/")
		if len(parts) < 9 {
			return nil, huma.Error400BadRequest("Invalid Azure VM resource ID", nil)
		}
		resourceGroupName := parts[4]
		vmName := parts[8]

		// Optimistic state
		optimistic := map[string]string{"start": "pending", "stop": "stopping", "restart": "pending"}[verb]
		if optimistic != "" {
			_ = a.queries.UpdateCloudInstanceAzureState(ctx, db.UpdateCloudInstanceAzureStateParams{
				CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(optimistic), Valid: true},
				CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
			})
		}

		// Invoke Azure API
		switch verb {
		case "start":
			poller, perr := client.BeginStart(ctx, resourceGroupName, vmName, nil)
			if perr == nil {
				_, err = poller.PollUntilDone(ctx, nil)
			} else {
				err = perr
			}
		case "stop":
			poller, perr := client.BeginPowerOff(ctx, resourceGroupName, vmName, nil)
			if perr == nil {
				_, err = poller.PollUntilDone(ctx, nil)
			} else {
				err = perr
			}
		case "restart":
			poller, perr := client.BeginRestart(ctx, resourceGroupName, vmName, nil)
			if perr == nil {
				_, err = poller.PollUntilDone(ctx, nil)
			} else {
				err = perr
			}
		default:
			return nil, huma.Error400BadRequest("Unsupported Azure action", nil)
		}
		if err != nil {
			return nil, huma.Error500InternalServerError(fmt.Sprintf("Failed to %s Azure VM", verb))
		}

		// Publish per-instance state check task via generic state-check queue
		if ch, err := a.getChannel(); err == nil {
			userID, _ := ctx.Value("userID").(string)
			expected := map[string]string{"start": "running", "stop": "stopped", "restart": "running"}[verb]
			// AccountID for Azure here is the secret name (tenant) used by state tracker
			// Resolve tenant again
			nodeIDPg, _ := converters.StringToPgTypeUUID(nodeID)
			userIDPg, _ := converters.StringToPgTypeUUID(userID)
			node, _ := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{NodeID: *nodeIDPg, ID: *userIDPg})
			instances, _ := a.queries.GetEngagementCloudInstances(ctx, node.EngagementID)
			tenantSecret := ""
			for _, ci := range instances {
				if ci.NodeID == node.NodeID && ci.AzureTenantID.Valid {
					if tid, err := converters.PgTypeUUIDToString(ci.AzureTenantID); err == nil {
						tenantSecret = *tid
					}
					break
				}
			}
			task := cloudstate.CloudInstanceStateCheckTask{
				Provider:      "AZURE",
				AccountID:     tenantSecret,
				InstanceID:    instanceID,
				NodeID:        nodeID,
				Action:        verb,
				UserID:        userID,
				ExpectedState: expected,
			}
			_ = cloudstate.PublishStateCheckTask(ctx, ch, "cloud-instance-state-check", task)
		}

		resp := &awsActionOutput{}
		resp.Body.Message = fmt.Sprintf("Azure %s requested for %s", verb, instanceID)
		resp.Body.State = optimistic
		return resp, nil
	}

	// Start
	huma.Register(api, huma.Operation{
		OperationID:   "post-azure-nodes-cloud-instance-start",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/azure/start",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Start an Azure Cloud Instance",
		Tags:          []string{"Nodes, Cloud Instance, AZURE"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		return azureStartStop(ctx, i.NodeID, "start")
	})

	// Stop
	huma.Register(api, huma.Operation{
		OperationID:   "post-azure-nodes-cloud-instance-stop",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/azure/stop",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Stop an Azure Cloud Instance",
		Tags:          []string{"Nodes, Cloud Instance, AZURE"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		return azureStartStop(ctx, i.NodeID, "stop")
	})

	// Restart
	huma.Register(api, huma.Operation{
		OperationID:   "post-azure-nodes-cloud-instance-restart",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/azure/restart",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Restart an Azure Cloud Instance",
		Tags:          []string{"Nodes, Cloud Instance, AZURE"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		return azureStartStop(ctx, i.NodeID, "restart")
	})

	// Terminate (Azure)
	huma.Register(api, huma.Operation{
		OperationID:   "post-azure-nodes-cloud-instance-terminate",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/azure/terminate",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Terminate an Azure Cloud Instance",
		Tags:          []string{"Nodes, Cloud Instance, AZURE"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		client, instanceID, err := getAzureClientForNode(ctx, i.NodeID)
		if err != nil {
			return nil, huma.Error400BadRequest("Failed to prepare Azure client", err)
		}
		parts := strings.Split(instanceID, "/")
		resourceGroupName := parts[4]
		vmName := parts[8]

		// Capture context for logging and async check
		userID, _ := ctx.Value("userID").(string)
		var currentIP string
		if nodeIDPg, err := converters.StringToPgTypeUUID(i.NodeID); err == nil {
			if userIDPg, err := converters.StringToPgTypeUUID(userID); err == nil {
				if node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{NodeID: *nodeIDPg, ID: *userIDPg}); err == nil {
					if node.PublicIpv4Address != nil {
						currentIP = node.PublicIpv4Address.String()
					}
				}
			}
		}

		// Delete the VM resource (this also releases NIC/PIP depending on Azure config)
		poller, terr := client.BeginDelete(ctx, resourceGroupName, vmName, nil)
		if terr != nil {
			return nil, huma.Error500InternalServerError("Failed to terminate Azure VM")
		}
		if _, err = poller.PollUntilDone(ctx, nil); err != nil {
			return nil, huma.Error500InternalServerError("Azure VM terminate polling failed")
		}
		_ = a.queries.UpdateCloudInstanceAzureState(ctx, db.UpdateCloudInstanceAzureStateParams{
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum("shutting-down"), Valid: true},
			CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
		})

		// Log and queue a state-check to confirm termination
		if userID != "" {
			userIDPg, _ := converters.StringToPgTypeUUID(userID)
			nodeIDPg, _ := converters.StringToPgTypeUUID(i.NodeID)
			ts := pgtype.Timestamp{Time: time.Now(), Valid: true}
			_ = activitylogs.InsertLog(a.queries, fmt.Sprintf("Azure instance terminate requested (%s)", instanceID), db.LogsNodesTypeEnumNODEUPDATE, *userIDPg, *nodeIDPg, ts)

			// Resolve Azure tenant secret for state checker
			tenantSecret := ""
			if node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{NodeID: *nodeIDPg, ID: *userIDPg}); err == nil {
				if instances, err := a.queries.GetEngagementCloudInstances(ctx, node.EngagementID); err == nil {
					for _, ci := range instances {
						if ci.NodeID == node.NodeID && ci.AzureTenantID.Valid {
							if tid, err := converters.PgTypeUUIDToString(ci.AzureTenantID); err == nil {
								tenantSecret = *tid
							}
							break
						}
					}
				}
			}

			if ch, err := a.getChannel(); err == nil {
				task := cloudstate.CloudInstanceStateCheckTask{
					Provider:      "AZURE",
					AccountID:     tenantSecret,
					InstanceID:    instanceID,
					NodeID:        i.NodeID,
					Action:        "terminate",
					UserID:        userID,
					OldIP:         currentIP,
					ExpectedState: "terminated",
				}
				_ = cloudstate.PublishStateCheckTask(ctx, ch, "cloud-instance-state-check", task)
			}
		}

		resp := &awsActionOutput{}
		resp.Body.Message = fmt.Sprintf("Azure terminate requested for %s", instanceID)
		resp.Body.State = "shutting-down"
		return resp, nil
	})

	// REAPI for Azure: stop then start in background, then publish check
	huma.Register(api, huma.Operation{
		OperationID:   "post-azure-nodes-cloud-instance-reapi",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/azure/reapi",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "ReAPI an Azure Cloud Instance: stop then start and refresh public IP",
		Tags:          []string{"Nodes, Cloud Instance, AZURE"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		// Kick off stop immediately, then start after deallocated
		client, instanceID, err := getAzureClientForNode(ctx, i.NodeID)
		if err != nil {
			return nil, huma.Error400BadRequest("Failed to prepare Azure client", err)
		}
		parts := strings.Split(instanceID, "/")
		resourceGroupName := parts[4]
		vmName := parts[8]

		// Capture current IP and log request (similar to AWS)
		var currentIP string
		if userID, ok := ctx.Value("userID").(string); ok {
			if nodeIDPg, err := converters.StringToPgTypeUUID(i.NodeID); err == nil {
				if userIDPg, err := converters.StringToPgTypeUUID(userID); err == nil {
					if node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{NodeID: *nodeIDPg, ID: *userIDPg}); err == nil {
						if node.PublicIpv4Address != nil {
							currentIP = node.PublicIpv4Address.String()
						}
						// No initial REAPI request log; state checker will log success with IP details
						ts := pgtype.Timestamp{Time: time.Now(), Valid: true}
						_ = ts // keep ts in case needed later; avoid unused var
					}
				}
			}
		}

		// Use Deallocate to force public IP release for dynamic IPs
		pollerDealloc, perr := client.BeginDeallocate(ctx, resourceGroupName, vmName, nil)
		if perr != nil {
			return nil, huma.Error500InternalServerError("Failed to deallocate Azure VM")
		}
		if _, err = pollerDealloc.PollUntilDone(ctx, nil); err != nil {
			return nil, huma.Error500InternalServerError("Azure VM deallocate polling failed")
		}
		_ = a.queries.UpdateCloudInstanceAzureState(ctx, db.UpdateCloudInstanceAzureStateParams{
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum("stopped"), Valid: true},
			CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
		})

		// Background waiter to reach deallocated, then start, then publish check
		go func(currentIP string) {
			// Simple delay then start
			time.Sleep(2 * time.Minute)
			pollerStart, _ := client.BeginStart(context.Background(), resourceGroupName, vmName, nil)
			_, _ = pollerStart.PollUntilDone(context.Background(), nil)
			_ = a.queries.UpdateCloudInstanceAzureState(context.Background(), db.UpdateCloudInstanceAzureStateParams{
				CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum("pending"), Valid: true},
				CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
			})
			if ch, err := a.getChannel(); err == nil {
				userID, _ := ctx.Value("userID").(string)
				// Resolve tenant secret
				nodeIDPg, _ := converters.StringToPgTypeUUID(i.NodeID)
				userIDPg, _ := converters.StringToPgTypeUUID(userID)
				node, _ := a.queries.GetNodeTypeCloudInstance(context.Background(), db.GetNodeTypeCloudInstanceParams{NodeID: *nodeIDPg, ID: *userIDPg})
				instances, _ := a.queries.GetEngagementCloudInstances(context.Background(), node.EngagementID)
				tenantSecret := ""
				for _, ci := range instances {
					if ci.NodeID == node.NodeID && ci.AzureTenantID.Valid {
						if tid, err := converters.PgTypeUUIDToString(ci.AzureTenantID); err == nil {
							tenantSecret = *tid
						}
						break
					}
				}
				task := cloudstate.CloudInstanceStateCheckTask{
					Provider:      "AZURE",
					AccountID:     tenantSecret,
					InstanceID:    instanceID,
					NodeID:        i.NodeID,
					Action:        "reapi",
					UserID:        userID,
					OldIP:         currentIP,
					ExpectedState: "running",
				}
				_ = cloudstate.PublishStateCheckTask(context.Background(), ch, "cloud-instance-state-check", task)
			}
		}(currentIP)

		resp := &awsActionOutput{}
		resp.Body.Message = "Azure REAPI initiated"
		return resp, nil
	})

	// Stop instance
	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-cloud-instance-stop",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/stop",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Stop an AWS Cloud Instance",
		Tags:          []string{"Nodes, Cloud Instance, AWS"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		client, instanceID, err := getAwsClientForNode(ctx, i.NodeID)
		if err != nil {
			return nil, huma.Error400BadRequest("Failed to prepare AWS client", err)
		}

		// Get node details for async state tracking
		nodeIDPg, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			a.logger.Error("Invalid node ID for stop action", "error", err)
		}

		userID, _ := ctx.Value("userID").(string)
		var currentIP, accountIDStr, region string
		var engagementID pgtype.UUID
		var currentNodeID pgtype.UUID

		if userID != "" && nodeIDPg != nil {
			if userIDPg, err := converters.StringToPgTypeUUID(userID); err == nil {
				if node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{
					NodeID: *nodeIDPg,
					ID:     *userIDPg,
				}); err == nil {
					if node.PublicIpv4Address != nil {
						currentIP = node.PublicIpv4Address.String()
					}
					region = node.Region
					engagementID = node.EngagementID
					currentNodeID = node.NodeID

					// Get AWS account ID
					if instances, err := a.queries.GetEngagementCloudInstances(ctx, engagementID); err == nil {
						for _, ci := range instances {
							if ci.NodeID == node.NodeID {
								if accountID, err := converters.PgTypeUUIDToString(ci.AwsAccountID); err == nil {
									accountIDStr = *accountID
								}
								break
							}
						}
					}
				}
			}
		}

		out, err := client.StopInstances(context.TODO(), &ec2.StopInstancesInput{InstanceIds: []string{instanceID}})
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to stop instance")
		}
		state := "stopping"
		if len(out.StoppingInstances) > 0 {
			state = string(out.StoppingInstances[0].CurrentState.Name)
		}
		_ = a.queries.UpdateCloudInstanceAWSState(ctx, db.UpdateCloudInstanceAWSStateParams{
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(state), Valid: true},
			CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
		})

		// Log immediate action and queue async state check
		if userID != "" && nodeIDPg != nil {
			userIDPg, _ := converters.StringToPgTypeUUID(userID)
			ts := pgtype.Timestamp{Time: time.Now(), Valid: true}

			logMessage := fmt.Sprintf("AWS instance stop requested (%s)", instanceID)
			_ = activitylogs.InsertLog(a.queries, logMessage, db.LogsNodesTypeEnumNODEUPDATE, *userIDPg, *nodeIDPg, ts)

			// Queue single-instance check task on existing AWS sync queue
			if accountIDStr != "" && region != "" {
				secretID := ""
				if instances, err := a.queries.GetEngagementCloudInstances(ctx, engagementID); err == nil {
					for _, ci := range instances {
						if ci.NodeID == currentNodeID {
							secretID = ci.AwsAccountID.String()
							break
						}
					}
				}
				if secretID != "" {
					task := awsScheduler.SingleInstanceCheckTask{
						Task:          "single_instance_check",
						Provider:      "AWS",
						SecretID:      secretID,
						AccountID:     accountIDStr,
						Region:        region,
						InstanceID:    instanceID,
						NodeID:        i.NodeID,
						Action:        "stop",
						UserID:        userID,
						OldIP:         currentIP,
						ExpectedState: "stopped",
					}
					if ch, err := a.getChannel(); err == nil {
						a.logger.Info("Queueing single-instance check", "action", task.Action, "node_id", task.NodeID, "instance_id", task.InstanceID, "account_id", task.AccountID, "region", task.Region)
						if err := awsScheduler.PublishSingleInstanceCheckTask(ctx, ch, "cloud-instance-sync-queue", task); err != nil {
							a.logger.Error("Publish single-instance check failed", "error", err)
						} else {
							a.logger.Info("Published single-instance check", "queue", "cloud-instance-sync-queue", "action", task.Action, "instance_id", task.InstanceID)
						}
					} else {
						a.logger.Error("Failed to get RabbitMQ channel", "error", err)
					}
				}
			}
		}
		resp := &awsActionOutput{}
		resp.Body.Message = fmt.Sprintf("Stop requested for %s", instanceID)
		resp.Body.State = state
		return resp, nil
	})

	// Reboot instance
	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-cloud-instance-reboot",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/reboot",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Reboot an AWS Cloud Instance",
		Tags:          []string{"Nodes, Cloud Instance, AWS"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		client, instanceID, err := getAwsClientForNode(ctx, i.NodeID)
		if err != nil {
			return nil, huma.Error400BadRequest("Failed to prepare AWS client", err)
		}

		// Get node details for async state tracking
		nodeIDPg, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			a.logger.Error("Invalid node ID for reboot action", "error", err)
		}

		userID, _ := ctx.Value("userID").(string)
		var currentIP, accountIDStr, region string
		var engagementID pgtype.UUID
		var currentNodeID pgtype.UUID

		if userID != "" && nodeIDPg != nil {
			if userIDPg, err := converters.StringToPgTypeUUID(userID); err == nil {
				if node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{
					NodeID: *nodeIDPg,
					ID:     *userIDPg,
				}); err == nil {
					if node.PublicIpv4Address != nil {
						currentIP = node.PublicIpv4Address.String()
					}
					region = node.Region
					engagementID = node.EngagementID
					currentNodeID = node.NodeID

					// Get AWS account ID
					if instances, err := a.queries.GetEngagementCloudInstances(ctx, engagementID); err == nil {
						for _, ci := range instances {
							if ci.NodeID == node.NodeID {
								if accountID, err := converters.PgTypeUUIDToString(ci.AwsAccountID); err == nil {
									accountIDStr = *accountID
								}
								break
							}
						}
					}
				}
			}
		}

		_, err = client.RebootInstances(context.TODO(), &ec2.RebootInstancesInput{InstanceIds: []string{instanceID}})
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to reboot instance")
		}

		// Log immediate action and queue async state check
		if userID != "" && nodeIDPg != nil {
			userIDPg, _ := converters.StringToPgTypeUUID(userID)
			ts := pgtype.Timestamp{Time: time.Now(), Valid: true}

			logMessage := fmt.Sprintf("AWS instance reboot requested (%s)", instanceID)
			_ = activitylogs.InsertLog(a.queries, logMessage, db.LogsNodesTypeEnumNODEUPDATE, *userIDPg, *nodeIDPg, ts)

			// Queue single-instance check task on existing AWS sync queue
			if accountIDStr != "" && region != "" {
				secretID := ""
				if instances, err := a.queries.GetEngagementCloudInstances(ctx, engagementID); err == nil {
					for _, ci := range instances {
						if ci.NodeID == currentNodeID {
							secretID = ci.AwsAccountID.String()
							break
						}
					}
				}
				if secretID != "" {
					task := awsScheduler.SingleInstanceCheckTask{
						Task:          "single_instance_check",
						Provider:      "AWS",
						SecretID:      secretID,
						AccountID:     accountIDStr,
						Region:        region,
						InstanceID:    instanceID,
						NodeID:        i.NodeID,
						Action:        "reboot",
						UserID:        userID,
						OldIP:         currentIP,
						ExpectedState: "running",
					}
					if ch, err := a.getChannel(); err == nil {
						a.logger.Info("Queueing single-instance check", "action", task.Action, "node_id", task.NodeID, "instance_id", task.InstanceID, "account_id", task.AccountID, "region", task.Region)
						if err := awsScheduler.PublishSingleInstanceCheckTask(ctx, ch, "cloud-instance-sync-queue", task); err != nil {
							a.logger.Error("Publish single-instance check failed", "error", err)
						} else {
							a.logger.Info("Published single-instance check", "queue", "cloud-instance-sync-queue", "action", task.Action, "instance_id", task.InstanceID)
						}
					} else {
						a.logger.Error("Failed to get RabbitMQ channel", "error", err)
					}
				}
			}
		}
		resp := &awsActionOutput{}
		resp.Body.Message = fmt.Sprintf("Reboot requested for %s", instanceID)
		// State typically remains running
		return resp, nil
	})

	// Terminate instance
	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-cloud-instance-terminate",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/terminate",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Terminate an AWS Cloud Instance",
		Tags:          []string{"Nodes, Cloud Instance, AWS"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		client, instanceID, err := getAwsClientForNode(ctx, i.NodeID)
		if err != nil {
			return nil, huma.Error400BadRequest("Failed to prepare AWS client", err)
		}

		// Get node details for async state tracking
		nodeIDPg, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			a.logger.Error("Invalid node ID for terminate action", "error", err)
		}

		userID, _ := ctx.Value("userID").(string)
		var currentIP, accountIDStr, region string
		var engagementID pgtype.UUID
		var currentNodeID pgtype.UUID

		if userID != "" && nodeIDPg != nil {
			if userIDPg, err := converters.StringToPgTypeUUID(userID); err == nil {
				if node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{
					NodeID: *nodeIDPg,
					ID:     *userIDPg,
				}); err == nil {
					if node.PublicIpv4Address != nil {
						currentIP = node.PublicIpv4Address.String()
					}
					region = node.Region
					engagementID = node.EngagementID
					currentNodeID = node.NodeID

					// Get AWS account ID
					if instances, err := a.queries.GetEngagementCloudInstances(ctx, node.EngagementID); err == nil {
						for _, ci := range instances {
							if ci.NodeID == node.NodeID {
								if accountID, err := converters.PgTypeUUIDToString(ci.AwsAccountID); err == nil {
									accountIDStr = *accountID
								}
								break
							}
						}
					}
				}
			}
		}

		out, err := client.TerminateInstances(context.TODO(), &ec2.TerminateInstancesInput{InstanceIds: []string{instanceID}})
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to terminate instance")
		}
		state := "shutting-down"
		if len(out.TerminatingInstances) > 0 {
			state = string(out.TerminatingInstances[0].CurrentState.Name)
		}
		_ = a.queries.UpdateCloudInstanceAWSState(ctx, db.UpdateCloudInstanceAWSStateParams{
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(state), Valid: true},
			CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
		})

		// Log immediate action and queue async state check
		if userID != "" && nodeIDPg != nil {
			userIDPg, _ := converters.StringToPgTypeUUID(userID)
			ts := pgtype.Timestamp{Time: time.Now(), Valid: true}

			logMessage := fmt.Sprintf("AWS instance terminate requested (%s)", instanceID)
			_ = activitylogs.InsertLog(a.queries, logMessage, db.LogsNodesTypeEnumNODEUPDATE, *userIDPg, *nodeIDPg, ts)

			// Queue single-instance check task on existing AWS sync queue
			if accountIDStr != "" && region != "" {
				secretID := ""
				// reuse engagementID/currentNodeID captured above
				if instances, err := a.queries.GetEngagementCloudInstances(ctx, engagementID); err == nil {
					for _, ci := range instances {
						if ci.NodeID == currentNodeID {
							secretID = ci.AwsAccountID.String()
							break
						}
					}
				}
				if secretID != "" {
					task := awsScheduler.SingleInstanceCheckTask{
						Task:          "single_instance_check",
						Provider:      "AWS",
						SecretID:      secretID,
						AccountID:     accountIDStr,
						Region:        region,
						InstanceID:    instanceID,
						NodeID:        i.NodeID,
						Action:        "terminate",
						UserID:        userID,
						OldIP:         currentIP,
						ExpectedState: "terminated",
					}
					if ch, err := a.getChannel(); err == nil {
						a.logger.Info("Queueing single-instance check", "action", task.Action, "node_id", task.NodeID, "instance_id", task.InstanceID, "account_id", task.AccountID, "region", task.Region)
						if err := awsScheduler.PublishSingleInstanceCheckTask(ctx, ch, "cloud-instance-sync-queue", task); err != nil {
							a.logger.Error("Publish single-instance check failed", "error", err)
						} else {
							a.logger.Info("Published single-instance check", "queue", "cloud-instance-sync-queue", "action", task.Action, "instance_id", task.InstanceID)
						}
					} else {
						a.logger.Error("Failed to get RabbitMQ channel", "error", err)
					}
				}
			}
		}
		resp := &awsActionOutput{}
		resp.Body.Message = fmt.Sprintf("Terminate requested for %s", instanceID)
		resp.Body.State = state
		return resp, nil
	})

	// Start instance (only when stopped)
	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-cloud-instance-start",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/start",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Start an AWS Cloud Instance (only when stopped)",
		Tags:          []string{"Nodes, Cloud Instance, AWS"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		client, instanceID, err := getAwsClientForNode(ctx, i.NodeID)
		if err != nil {
			return nil, huma.Error400BadRequest("Failed to prepare AWS client", err)
		}

		// Get node details for async state tracking
		nodeIDPg, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			a.logger.Error("Invalid node ID for start action", "error", err)
		}

		userID, _ := ctx.Value("userID").(string)
		var oldIP, accountIDStr, region string
		var engagementID pgtype.UUID
		var currentNodeID pgtype.UUID

		if userID != "" && nodeIDPg != nil {
			if userIDPg, err := converters.StringToPgTypeUUID(userID); err == nil {
				if node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{
					NodeID: *nodeIDPg,
					ID:     *userIDPg,
				}); err == nil {
					if node.PublicIpv4Address != nil {
						oldIP = node.PublicIpv4Address.String()
					}
					region = node.Region
					engagementID = node.EngagementID
					currentNodeID = node.NodeID

					// Get AWS account ID
					if instances, err := a.queries.GetEngagementCloudInstances(ctx, engagementID); err == nil {
						for _, ci := range instances {
							if ci.NodeID == node.NodeID {
								if accountID, err := converters.PgTypeUUIDToString(ci.AwsAccountID); err == nil {
									accountIDStr = *accountID
								}
								break
							}
						}
					}
				}
			}
		}

		out, err := client.StartInstances(context.TODO(), &ec2.StartInstancesInput{InstanceIds: []string{instanceID}})
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to start instance")
		}
		state := "pending"
		if len(out.StartingInstances) > 0 {
			state = string(out.StartingInstances[0].CurrentState.Name)
		}
		_ = a.queries.UpdateCloudInstanceAWSState(ctx, db.UpdateCloudInstanceAWSStateParams{
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(state), Valid: true},
			CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
		})

		// Log immediate action and queue async state check
		if userID != "" && nodeIDPg != nil {
			userIDPg, _ := converters.StringToPgTypeUUID(userID)
			ts := pgtype.Timestamp{Time: time.Now(), Valid: true}

			logMessage := fmt.Sprintf("AWS instance start requested (%s)", instanceID)
			_ = activitylogs.InsertLog(a.queries, logMessage, db.LogsNodesTypeEnumNODEUPDATE, *userIDPg, *nodeIDPg, ts)

			// Queue single-instance check task on existing AWS sync queue
			if accountIDStr != "" && region != "" {
				secretID := ""
				if instances, err := a.queries.GetEngagementCloudInstances(ctx, engagementID); err == nil {
					for _, ci := range instances {
						if ci.NodeID == currentNodeID {
							secretID = ci.AwsAccountID.String()
							break
						}
					}
				}
				if secretID != "" {
					task := awsScheduler.SingleInstanceCheckTask{
						Task:          "single_instance_check",
						Provider:      "AWS",
						SecretID:      secretID,
						AccountID:     accountIDStr,
						Region:        region,
						InstanceID:    instanceID,
						NodeID:        i.NodeID,
						Action:        "start",
						UserID:        userID,
						OldIP:         oldIP,
						ExpectedState: "running",
					}
					if ch, err := a.getChannel(); err == nil {
						_ = awsScheduler.PublishSingleInstanceCheckTask(ctx, ch, "cloud-instance-sync-queue", task)
					}
				}
			}
		}
		resp := &awsActionOutput{}
		resp.Body.Message = fmt.Sprintf("Start requested for %s", instanceID)
		resp.Body.State = state
		return resp, nil
	})

	// REAPI (stop + start, add new API address)
	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-cloud-instance-reapi",
		Method:        http.MethodPost,
		Path:          "/nodes/cloud_instance/{nodeID}/reapi",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "ReAPI an AWS Cloud Instance: stop then start and refresh public IP",
		Tags:          []string{"Nodes, Cloud Instance, AWS"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		NodeID string `path:"nodeID"`
	}) (*awsActionOutput, error) {
		client, instanceID, err := getAwsClientForNode(ctx, i.NodeID)
		if err != nil {
			return nil, huma.Error400BadRequest("Failed to prepare AWS client", err)
		}
		// Stop
		_, err = client.StopInstances(context.TODO(), &ec2.StopInstancesInput{InstanceIds: []string{instanceID}})
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to stop instance")
		}
		_ = a.queries.UpdateCloudInstanceAWSState(ctx, db.UpdateCloudInstanceAWSStateParams{
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum("stopping"), Valid: true},
			CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
		})

		// Do not block this request; orchestrate stop->start in background. Gather details first.
		nodeIDPg, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Invalid node ID")
		}

		userID, _ := ctx.Value("userID").(string)
		if userID == "" {
			return nil, huma.Error401Unauthorized("User not authenticated")
		}
		userIDPg, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Invalid user ID")
		}

		node, err := a.queries.GetNodeTypeCloudInstance(ctx, db.GetNodeTypeCloudInstanceParams{
			NodeID: *nodeIDPg,
			ID:     *userIDPg,
		})
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to get node details")
		}
		var oldIP string
		if node.PublicIpv4Address != nil {
			oldIP = node.PublicIpv4Address.String()
		}
		instances, err := a.queries.GetEngagementCloudInstances(ctx, node.EngagementID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to get engagement instances")
		}
		var awsAccountID pgtype.UUID
		for _, ci := range instances {
			if ci.NodeID == node.NodeID {
				awsAccountID = ci.AwsAccountID
				break
			}
		}
		if !awsAccountID.Valid {
			return nil, huma.Error500InternalServerError("Could not resolve AWS account for node")
		}
		accountIDStr, err := converters.PgTypeUUIDToString(awsAccountID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Invalid AWS account ID")
		}
		secretID := ""
		for _, ci := range instances {
			if ci.NodeID == node.NodeID {
				secretID = ci.AwsAccountID.String()
				break
			}
		}

		// Launch background goroutine to wait for stopped, then start, then publish check
		go func(instanceID, region, nodeID, userID, oldIP, accountID, secretID string) {
			// Wait until instance fully stops before starting again
			stoppedWaiter := ec2.NewInstanceStoppedWaiter(client)
			if err := stoppedWaiter.Wait(context.Background(), &ec2.DescribeInstancesInput{InstanceIds: []string{instanceID}}, 10*time.Minute); err != nil {
				a.logger.Error("REAPI background: instance did not stop in time", "instance_id", instanceID, "error", err)
				return
			}
			// Start
			if _, err := client.StartInstances(context.Background(), &ec2.StartInstancesInput{InstanceIds: []string{instanceID}}); err != nil {
				a.logger.Error("REAPI background: failed to start", "instance_id", instanceID, "error", err)
				return
			}
			// Update to pending
			_ = a.queries.UpdateCloudInstanceAWSState(context.Background(), db.UpdateCloudInstanceAWSStateParams{
				CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum("pending"), Valid: true},
				CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
			})
			// Publish single-instance check to observe when running and capture new IP
			task := awsScheduler.SingleInstanceCheckTask{
				Task:          "single_instance_check",
				Provider:      "AWS",
				SecretID:      secretID,
				AccountID:     accountID,
				Region:        region,
				InstanceID:    instanceID,
				NodeID:        nodeID,
				Action:        "reapi",
				UserID:        userID,
				OldIP:         oldIP,
				ExpectedState: "running",
			}
			if ch, err := a.getChannel(); err == nil {
				if err := awsScheduler.PublishSingleInstanceCheckTask(context.Background(), ch, "cloud-instance-sync-queue", task); err != nil {
					a.logger.Error("REAPI background: publish single-instance check failed", "error", err)
				}
			} else {
				a.logger.Error("REAPI background: failed to get RabbitMQ channel", "error", err)
			}
		}(instanceID, node.Region, i.NodeID, userID, oldIP, *accountIDStr, secretID)

		resp := &awsActionOutput{}
		resp.Body.Message = "REAPI initiated"
		return resp, nil
	})

}
