/**
 * Generated by orval v7.11.2 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import { useMutation, useQuery } from "@tanstack/react-query";
import type {
  DataTag,
  DefinedInitialDataOptions,
  DefinedUseQueryResult,
  MutationFunction,
  QueryClient,
  QueryFunction,
  QueryKey,
  UndefinedInitialDataOptions,
  UseMutationOptions,
  UseMutationResult,
  UseQueryOptions,
  UseQueryResult,
} from "@tanstack/react-query";

import type {
  AwsActionOutputBody,
  AzureTenantForm,
  CreateAWSAccountInputBody,
  CreateAdminScriptInputBody,
  CreateAdminScriptOutputBody,
  CreateClientInputBody,
  CreateEngagementInputBody,
  CreateNodeEmailAddressInputBody,
  CreateNodeTypeHostInputBody,
  CreateNodeTypePersonInputBody,
  CreateNodeTypeUrlInputBody,
  CreateNodeTypeUrlOutputB<PERSON>,
  CreateScriptInputBody,
  CreateScriptOutputBody,
  DeactivateEngagementInputBody,
  EditAdminScriptInputBody,
  EditAdminScriptOutputBody,
  EditDomainsInputBody,
  EditDomainsOutputBody,
  EditEngagementInputBody,
  EditEngagementOutputBody,
  EditNodeGroupInputBody,
  EditNodeTypeCloudInstanceInputBody,
  EditNodeTypeCloudInstanceOutputBody,
  EditNodeTypeEmailAddressInputBody,
  EditNodeTypeEmailAddressOutputBody,
  EditNodeTypeHostInputBody,
  EditNodeTypePersonInputBody,
  EditNodeTypeUrlInputBody,
  EditNodeTypeUrlOutputBody,
  EditScriptInputBody,
  EditScriptOutputBody,
  EditUserUsernameInputBody,
  ErrorModel,
  GetAWSAccountsOutputBody,
  GetAdminScriptsOutputBody,
  GetAmisOutputBody,
  GetAwsInstanceTypesParams,
  GetAzureAmisOutputBody,
  GetAzureTenantsOutputBody,
  GetClientsOutputBody,
  GetCloudAccountsOutputBody,
  GetDeploymentOutputBody,
  GetDeploymentsOutputBody,
  GetDomainsOutputBody,
  GetEngagementCloudInstancesOutputBody,
  GetEngagementGraphOutputBody,
  GetEngagementGraphsOutputBody,
  GetEngagementOutputBody,
  GetEngagementParams,
  GetEngagementsOutputBody,
  GetInstanceTypesDBOutputBody,
  GetInstanceTypesOutputBody,
  GetInventoryCloudInstancesOutputBody,
  GetInventoryDomainsOutputBody,
  GetInventoryEmailAddressesOutputBody,
  GetInventoryHostsOutputBody,
  GetInventoryOutputBody,
  GetInventoryPersonsOutputBody,
  GetInventoryUrlsOutputBody,
  GetNodeCloudInstanceOutputBody,
  GetNodeTypeEmailAddressOutputBody,
  GetNodeTypeHostOutputBody,
  GetNodeTypePersonOutputBody,
  GetNodeTypeUrlOutputBody,
  GetNodesCloudInstanceParams,
  GetNodesEmailAddressParams,
  GetNodesHostParams,
  GetNodesPersonParams,
  GetNodesUrlParams,
  GetProvidersAzureRegionsParams,
  GetRegionsOutputBody,
  GetScriptsOutputBody,
  GetUniqueRegistrarsOutputBody,
  GetUserAssignmentLogsOutputBody,
  GetUserAssignmentsLogsOutputBody,
  GetUserDetailsOutputBody,
  GetUserEngagementTitlesOutputBody,
  GetUserManagementOutputBody,
  GetUserManagementUsersParams,
  GetUsersOutputBody,
  ImportDomainsBase64InputBody,
  ImportDomainsOutputBody,
  NodeCloudInstanceInputBody,
  NodeRelationshipInputBody,
  SetInstanceTypeRequest,
  SetSshKeyInputBody,
  SetSshKeyOutputBody,
  UpdateDomainFieldInputBody,
  UpdateDomainFieldOutputBody,
} from "./model";
import { customInstance } from "./services/axiosInstance";
import type { BodyType, ErrorType } from "./services/axiosInstance";

// https://stackoverflow.com/questions/49579094/typescript-conditional-types-filter-out-readonly-properties-pick-only-requir/49579497#49579497
type IfEquals<X, Y, A = X, B = never> =
  (<T>() => T extends X ? 1 : 2) extends <T>() => T extends Y ? 1 : 2 ? A : B;

type WritableKeys<T> = {
  [P in keyof T]-?: IfEquals<
    { [Q in P]: T[P] },
    { -readonly [Q in P]: T[P] },
    P
  >;
}[keyof T];

type UnionToIntersection<U> = (U extends any ? (k: U) => void : never) extends (
  k: infer I,
) => void
  ? I
  : never;
type DistributeReadOnlyOverUnions<T> = T extends any ? NonReadonly<T> : never;

type Writable<T> = Pick<T, WritableKeys<T>>;
type NonReadonly<T> = [T] extends [UnionToIntersection<T>]
  ? {
      [P in keyof Writable<T>]: T[P] extends object
        ? NonReadonly<NonNullable<T[P]>>
        : T[P];
    }
  : DistributeReadOnlyOverUnions<T>;

type SecondParameter<T extends (...args: never) => unknown> = Parameters<T>[1];

/**
 * @summary Get Admin Scripts
 */
export const getAdminScripts = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetAdminScriptsOutputBody>(
    { url: `/admin/scripts`, method: "GET", signal },
    options,
  );
};

export const getGetAdminScriptsQueryKey = () => {
  return [`/admin/scripts`] as const;
};

export const getGetAdminScriptsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAdminScripts>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getAdminScripts>>, TError, TData>
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAdminScriptsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getAdminScripts>>> = ({
    signal,
  }) => getAdminScripts(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAdminScripts>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAdminScriptsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAdminScripts>>
>;
export type GetAdminScriptsQueryError = ErrorType<ErrorModel>;

export function useGetAdminScripts<
  TData = Awaited<ReturnType<typeof getAdminScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAdminScripts>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAdminScripts>>,
          TError,
          Awaited<ReturnType<typeof getAdminScripts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetAdminScripts<
  TData = Awaited<ReturnType<typeof getAdminScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAdminScripts>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAdminScripts>>,
          TError,
          Awaited<ReturnType<typeof getAdminScripts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetAdminScripts<
  TData = Awaited<ReturnType<typeof getAdminScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAdminScripts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Admin Scripts
 */

export function useGetAdminScripts<
  TData = Awaited<ReturnType<typeof getAdminScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAdminScripts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetAdminScriptsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Create Admin Script
 */
export const postAdminScript = (
  createAdminScriptInputBody: BodyType<NonReadonly<CreateAdminScriptInputBody>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<CreateAdminScriptOutputBody>(
    {
      url: `/admin/scripts`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createAdminScriptInputBody,
      signal,
    },
    options,
  );
};

export const getPostAdminScriptMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postAdminScript>>,
    TError,
    { data: BodyType<NonReadonly<CreateAdminScriptInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postAdminScript>>,
  TError,
  { data: BodyType<NonReadonly<CreateAdminScriptInputBody>> },
  TContext
> => {
  const mutationKey = ["postAdminScript"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postAdminScript>>,
    { data: BodyType<NonReadonly<CreateAdminScriptInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postAdminScript(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostAdminScriptMutationResult = NonNullable<
  Awaited<ReturnType<typeof postAdminScript>>
>;
export type PostAdminScriptMutationBody = BodyType<
  NonReadonly<CreateAdminScriptInputBody>
>;
export type PostAdminScriptMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create Admin Script
 */
export const usePostAdminScript = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postAdminScript>>,
      TError,
      { data: BodyType<NonReadonly<CreateAdminScriptInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postAdminScript>>,
  TError,
  { data: BodyType<NonReadonly<CreateAdminScriptInputBody>> },
  TContext
> => {
  const mutationOptions = getPostAdminScriptMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Delete Admin Script
 */
export const deleteAdminScript = (
  scriptId: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    { url: `/admin/scripts/${scriptId}`, method: "DELETE" },
    options,
  );
};

export const getDeleteAdminScriptMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteAdminScript>>,
    TError,
    { scriptId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteAdminScript>>,
  TError,
  { scriptId: string },
  TContext
> => {
  const mutationKey = ["deleteAdminScript"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteAdminScript>>,
    { scriptId: string }
  > = (props) => {
    const { scriptId } = props ?? {};

    return deleteAdminScript(scriptId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteAdminScriptMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteAdminScript>>
>;

export type DeleteAdminScriptMutationError = ErrorType<ErrorModel>;

/**
 * @summary Delete Admin Script
 */
export const useDeleteAdminScript = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof deleteAdminScript>>,
      TError,
      { scriptId: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof deleteAdminScript>>,
  TError,
  { scriptId: string },
  TContext
> => {
  const mutationOptions = getDeleteAdminScriptMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Edit Admin Script
 */
export const editAdminScript = (
  scriptId: string,
  editAdminScriptInputBody: BodyType<NonReadonly<EditAdminScriptInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<EditAdminScriptOutputBody>(
    {
      url: `/admin/scripts/${scriptId}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editAdminScriptInputBody,
    },
    options,
  );
};

export const getEditAdminScriptMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editAdminScript>>,
    TError,
    { scriptId: string; data: BodyType<NonReadonly<EditAdminScriptInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editAdminScript>>,
  TError,
  { scriptId: string; data: BodyType<NonReadonly<EditAdminScriptInputBody>> },
  TContext
> => {
  const mutationKey = ["editAdminScript"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editAdminScript>>,
    { scriptId: string; data: BodyType<NonReadonly<EditAdminScriptInputBody>> }
  > = (props) => {
    const { scriptId, data } = props ?? {};

    return editAdminScript(scriptId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditAdminScriptMutationResult = NonNullable<
  Awaited<ReturnType<typeof editAdminScript>>
>;
export type EditAdminScriptMutationBody = BodyType<
  NonReadonly<EditAdminScriptInputBody>
>;
export type EditAdminScriptMutationError = ErrorType<ErrorModel>;

/**
 * @summary Edit Admin Script
 */
export const useEditAdminScript = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editAdminScript>>,
      TError,
      {
        scriptId: string;
        data: BodyType<NonReadonly<EditAdminScriptInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editAdminScript>>,
  TError,
  { scriptId: string; data: BodyType<NonReadonly<EditAdminScriptInputBody>> },
  TContext
> => {
  const mutationOptions = getEditAdminScriptMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Create Client
 */
export const createClient = (
  createClientInputBody: BodyType<NonReadonly<CreateClientInputBody>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/client`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createClientInputBody,
      signal,
    },
    options,
  );
};

export const getCreateClientMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof createClient>>,
    TError,
    { data: BodyType<NonReadonly<CreateClientInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof createClient>>,
  TError,
  { data: BodyType<NonReadonly<CreateClientInputBody>> },
  TContext
> => {
  const mutationKey = ["createClient"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof createClient>>,
    { data: BodyType<NonReadonly<CreateClientInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return createClient(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type CreateClientMutationResult = NonNullable<
  Awaited<ReturnType<typeof createClient>>
>;
export type CreateClientMutationBody = BodyType<
  NonReadonly<CreateClientInputBody>
>;
export type CreateClientMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create Client
 */
export const useCreateClient = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof createClient>>,
      TError,
      { data: BodyType<NonReadonly<CreateClientInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof createClient>>,
  TError,
  { data: BodyType<NonReadonly<CreateClientInputBody>> },
  TContext
> => {
  const mutationOptions = getCreateClientMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get Clients
 */
export const getClients = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetClientsOutputBody>(
    { url: `/clients`, method: "GET", signal },
    options,
  );
};

export const getGetClientsQueryKey = () => {
  return [`/clients`] as const;
};

export const getGetClientsQueryOptions = <
  TData = Awaited<ReturnType<typeof getClients>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getClients>>, TError, TData>
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetClientsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getClients>>> = ({
    signal,
  }) => getClients(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getClients>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetClientsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getClients>>
>;
export type GetClientsQueryError = ErrorType<ErrorModel>;

export function useGetClients<
  TData = Awaited<ReturnType<typeof getClients>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getClients>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getClients>>,
          TError,
          Awaited<ReturnType<typeof getClients>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetClients<
  TData = Awaited<ReturnType<typeof getClients>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getClients>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getClients>>,
          TError,
          Awaited<ReturnType<typeof getClients>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetClients<
  TData = Awaited<ReturnType<typeof getClients>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getClients>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Clients
 */

export function useGetClients<
  TData = Awaited<ReturnType<typeof getClients>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getClients>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetClientsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Deployment Details
 */
export const getDeployment = (
  deploymentID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetDeploymentOutputBody>(
    { url: `/deployment/${deploymentID}`, method: "GET", signal },
    options,
  );
};

export const getGetDeploymentQueryKey = (deploymentID?: string) => {
  return [`/deployment/${deploymentID}`] as const;
};

export const getGetDeploymentQueryOptions = <
  TData = Awaited<ReturnType<typeof getDeployment>>,
  TError = ErrorType<ErrorModel>,
>(
  deploymentID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployment>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetDeploymentQueryKey(deploymentID);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDeployment>>> = ({
    signal,
  }) => getDeployment(deploymentID, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!deploymentID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getDeployment>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetDeploymentQueryResult = NonNullable<
  Awaited<ReturnType<typeof getDeployment>>
>;
export type GetDeploymentQueryError = ErrorType<ErrorModel>;

export function useGetDeployment<
  TData = Awaited<ReturnType<typeof getDeployment>>,
  TError = ErrorType<ErrorModel>,
>(
  deploymentID: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployment>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getDeployment>>,
          TError,
          Awaited<ReturnType<typeof getDeployment>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetDeployment<
  TData = Awaited<ReturnType<typeof getDeployment>>,
  TError = ErrorType<ErrorModel>,
>(
  deploymentID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployment>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getDeployment>>,
          TError,
          Awaited<ReturnType<typeof getDeployment>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetDeployment<
  TData = Awaited<ReturnType<typeof getDeployment>>,
  TError = ErrorType<ErrorModel>,
>(
  deploymentID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployment>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Deployment Details
 */

export function useGetDeployment<
  TData = Awaited<ReturnType<typeof getDeployment>>,
  TError = ErrorType<ErrorModel>,
>(
  deploymentID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployment>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetDeploymentQueryOptions(deploymentID, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Deployments
 */
export const getDeployments = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetDeploymentsOutputBody>(
    { url: `/deployments`, method: "GET", signal },
    options,
  );
};

export const getGetDeploymentsQueryKey = () => {
  return [`/deployments`] as const;
};

export const getGetDeploymentsQueryOptions = <
  TData = Awaited<ReturnType<typeof getDeployments>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getDeployments>>, TError, TData>
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDeploymentsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDeployments>>> = ({
    signal,
  }) => getDeployments(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getDeployments>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetDeploymentsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getDeployments>>
>;
export type GetDeploymentsQueryError = ErrorType<ErrorModel>;

export function useGetDeployments<
  TData = Awaited<ReturnType<typeof getDeployments>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployments>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getDeployments>>,
          TError,
          Awaited<ReturnType<typeof getDeployments>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetDeployments<
  TData = Awaited<ReturnType<typeof getDeployments>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployments>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getDeployments>>,
          TError,
          Awaited<ReturnType<typeof getDeployments>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetDeployments<
  TData = Awaited<ReturnType<typeof getDeployments>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployments>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Deployments
 */

export function useGetDeployments<
  TData = Awaited<ReturnType<typeof getDeployments>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDeployments>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetDeploymentsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get all domains
 */
export const getDomains = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetDomainsOutputBody>(
    { url: `/domains`, method: "GET", signal },
    options,
  );
};

export const getGetDomainsQueryKey = () => {
  return [`/domains`] as const;
};

export const getGetDomainsQueryOptions = <
  TData = Awaited<ReturnType<typeof getDomains>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getDomains>>, TError, TData>
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetDomainsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getDomains>>> = ({
    signal,
  }) => getDomains(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getDomains>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetDomainsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getDomains>>
>;
export type GetDomainsQueryError = ErrorType<ErrorModel>;

export function useGetDomains<
  TData = Awaited<ReturnType<typeof getDomains>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDomains>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getDomains>>,
          TError,
          Awaited<ReturnType<typeof getDomains>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetDomains<
  TData = Awaited<ReturnType<typeof getDomains>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDomains>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getDomains>>,
          TError,
          Awaited<ReturnType<typeof getDomains>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetDomains<
  TData = Awaited<ReturnType<typeof getDomains>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDomains>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get all domains
 */

export function useGetDomains<
  TData = Awaited<ReturnType<typeof getDomains>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getDomains>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetDomainsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get unique registrar values
 */
export const getUniqueRegistrars = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetUniqueRegistrarsOutputBody>(
    { url: `/domains/registrars`, method: "GET", signal },
    options,
  );
};

export const getGetUniqueRegistrarsQueryKey = () => {
  return [`/domains/registrars`] as const;
};

export const getGetUniqueRegistrarsQueryOptions = <
  TData = Awaited<ReturnType<typeof getUniqueRegistrars>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getUniqueRegistrars>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUniqueRegistrarsQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getUniqueRegistrars>>
  > = ({ signal }) => getUniqueRegistrars(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUniqueRegistrars>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUniqueRegistrarsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getUniqueRegistrars>>
>;
export type GetUniqueRegistrarsQueryError = ErrorType<ErrorModel>;

export function useGetUniqueRegistrars<
  TData = Awaited<ReturnType<typeof getUniqueRegistrars>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUniqueRegistrars>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUniqueRegistrars>>,
          TError,
          Awaited<ReturnType<typeof getUniqueRegistrars>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUniqueRegistrars<
  TData = Awaited<ReturnType<typeof getUniqueRegistrars>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUniqueRegistrars>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUniqueRegistrars>>,
          TError,
          Awaited<ReturnType<typeof getUniqueRegistrars>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUniqueRegistrars<
  TData = Awaited<ReturnType<typeof getUniqueRegistrars>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUniqueRegistrars>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get unique registrar values
 */

export function useGetUniqueRegistrars<
  TData = Awaited<ReturnType<typeof getUniqueRegistrars>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUniqueRegistrars>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetUniqueRegistrarsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Edit Domains
 */
export const editDomains = (
  domainID: string,
  editDomainsInputBody: BodyType<NonReadonly<EditDomainsInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<EditDomainsOutputBody>(
    {
      url: `/domains/${domainID}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editDomainsInputBody,
    },
    options,
  );
};

export const getEditDomainsMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editDomains>>,
    TError,
    { domainID: string; data: BodyType<NonReadonly<EditDomainsInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editDomains>>,
  TError,
  { domainID: string; data: BodyType<NonReadonly<EditDomainsInputBody>> },
  TContext
> => {
  const mutationKey = ["editDomains"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editDomains>>,
    { domainID: string; data: BodyType<NonReadonly<EditDomainsInputBody>> }
  > = (props) => {
    const { domainID, data } = props ?? {};

    return editDomains(domainID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditDomainsMutationResult = NonNullable<
  Awaited<ReturnType<typeof editDomains>>
>;
export type EditDomainsMutationBody = BodyType<
  NonReadonly<EditDomainsInputBody>
>;
export type EditDomainsMutationError = ErrorType<ErrorModel>;

/**
 * @summary Edit Domains
 */
export const useEditDomains = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editDomains>>,
      TError,
      { domainID: string; data: BodyType<NonReadonly<EditDomainsInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editDomains>>,
  TError,
  { domainID: string; data: BodyType<NonReadonly<EditDomainsInputBody>> },
  TContext
> => {
  const mutationOptions = getEditDomainsMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Update a specific domain field
 */
export const updateDomainField = (
  domainID: string,
  updateDomainFieldInputBody: BodyType<NonReadonly<UpdateDomainFieldInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<UpdateDomainFieldOutputBody>(
    {
      url: `/domains/${domainID}/field`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: updateDomainFieldInputBody,
    },
    options,
  );
};

export const getUpdateDomainFieldMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof updateDomainField>>,
    TError,
    {
      domainID: string;
      data: BodyType<NonReadonly<UpdateDomainFieldInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof updateDomainField>>,
  TError,
  { domainID: string; data: BodyType<NonReadonly<UpdateDomainFieldInputBody>> },
  TContext
> => {
  const mutationKey = ["updateDomainField"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof updateDomainField>>,
    {
      domainID: string;
      data: BodyType<NonReadonly<UpdateDomainFieldInputBody>>;
    }
  > = (props) => {
    const { domainID, data } = props ?? {};

    return updateDomainField(domainID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type UpdateDomainFieldMutationResult = NonNullable<
  Awaited<ReturnType<typeof updateDomainField>>
>;
export type UpdateDomainFieldMutationBody = BodyType<
  NonReadonly<UpdateDomainFieldInputBody>
>;
export type UpdateDomainFieldMutationError = ErrorType<ErrorModel>;

/**
 * @summary Update a specific domain field
 */
export const useUpdateDomainField = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof updateDomainField>>,
      TError,
      {
        domainID: string;
        data: BodyType<NonReadonly<UpdateDomainFieldInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof updateDomainField>>,
  TError,
  { domainID: string; data: BodyType<NonReadonly<UpdateDomainFieldInputBody>> },
  TContext
> => {
  const mutationOptions = getUpdateDomainFieldMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Create Engagement
 */
export const postCreateEngagement = (
  createEngagementInputBody: BodyType<NonReadonly<CreateEngagementInputBody>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/engagement`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createEngagementInputBody,
      signal,
    },
    options,
  );
};

export const getPostCreateEngagementMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postCreateEngagement>>,
    TError,
    { data: BodyType<NonReadonly<CreateEngagementInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postCreateEngagement>>,
  TError,
  { data: BodyType<NonReadonly<CreateEngagementInputBody>> },
  TContext
> => {
  const mutationKey = ["postCreateEngagement"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postCreateEngagement>>,
    { data: BodyType<NonReadonly<CreateEngagementInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postCreateEngagement(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostCreateEngagementMutationResult = NonNullable<
  Awaited<ReturnType<typeof postCreateEngagement>>
>;
export type PostCreateEngagementMutationBody = BodyType<
  NonReadonly<CreateEngagementInputBody>
>;
export type PostCreateEngagementMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create Engagement
 */
export const usePostCreateEngagement = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postCreateEngagement>>,
      TError,
      { data: BodyType<NonReadonly<CreateEngagementInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postCreateEngagement>>,
  TError,
  { data: BodyType<NonReadonly<CreateEngagementInputBody>> },
  TContext
> => {
  const mutationOptions = getPostCreateEngagementMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get Engagements
 */
export const getEngagements = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetEngagementsOutputBody>(
    { url: `/engagements`, method: "GET", signal },
    options,
  );
};

export const getGetEngagementsQueryKey = () => {
  return [`/engagements`] as const;
};

export const getGetEngagementsQueryOptions = <
  TData = Awaited<ReturnType<typeof getEngagements>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getEngagements>>, TError, TData>
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetEngagementsQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getEngagements>>> = ({
    signal,
  }) => getEngagements(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getEngagements>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetEngagementsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getEngagements>>
>;
export type GetEngagementsQueryError = ErrorType<ErrorModel>;

export function useGetEngagements<
  TData = Awaited<ReturnType<typeof getEngagements>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagements>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagements>>,
          TError,
          Awaited<ReturnType<typeof getEngagements>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagements<
  TData = Awaited<ReturnType<typeof getEngagements>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagements>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagements>>,
          TError,
          Awaited<ReturnType<typeof getEngagements>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagements<
  TData = Awaited<ReturnType<typeof getEngagements>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagements>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagements
 */

export function useGetEngagements<
  TData = Awaited<ReturnType<typeof getEngagements>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagements>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetEngagementsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Engagement
 */
export const getEngagement = (
  engagementID: string,
  params?: GetEngagementParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetEngagementOutputBody>(
    { url: `/engagements/${engagementID}`, method: "GET", params, signal },
    options,
  );
};

export const getGetEngagementQueryKey = (
  engagementID?: string,
  params?: GetEngagementParams,
) => {
  return [`/engagements/${engagementID}`, ...(params ? [params] : [])] as const;
};

export const getGetEngagementQueryOptions = <
  TData = Awaited<ReturnType<typeof getEngagement>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  params?: GetEngagementParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagement>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetEngagementQueryKey(engagementID, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getEngagement>>> = ({
    signal,
  }) => getEngagement(engagementID, params, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!engagementID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getEngagement>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetEngagementQueryResult = NonNullable<
  Awaited<ReturnType<typeof getEngagement>>
>;
export type GetEngagementQueryError = ErrorType<ErrorModel>;

export function useGetEngagement<
  TData = Awaited<ReturnType<typeof getEngagement>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  params: undefined | GetEngagementParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagement>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagement>>,
          TError,
          Awaited<ReturnType<typeof getEngagement>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagement<
  TData = Awaited<ReturnType<typeof getEngagement>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  params?: GetEngagementParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagement>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagement>>,
          TError,
          Awaited<ReturnType<typeof getEngagement>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagement<
  TData = Awaited<ReturnType<typeof getEngagement>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  params?: GetEngagementParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagement>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagement
 */

export function useGetEngagement<
  TData = Awaited<ReturnType<typeof getEngagement>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  params?: GetEngagementParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getEngagement>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetEngagementQueryOptions(
    engagementID,
    params,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Edit an Engagement
 */
export const editEngagement = (
  engagementID: string,
  editEngagementInputBody: BodyType<NonReadonly<EditEngagementInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<EditEngagementOutputBody>(
    {
      url: `/engagements/${engagementID}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editEngagementInputBody,
    },
    options,
  );
};

export const getEditEngagementMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editEngagement>>,
    TError,
    {
      engagementID: string;
      data: BodyType<NonReadonly<EditEngagementInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editEngagement>>,
  TError,
  {
    engagementID: string;
    data: BodyType<NonReadonly<EditEngagementInputBody>>;
  },
  TContext
> => {
  const mutationKey = ["editEngagement"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editEngagement>>,
    {
      engagementID: string;
      data: BodyType<NonReadonly<EditEngagementInputBody>>;
    }
  > = (props) => {
    const { engagementID, data } = props ?? {};

    return editEngagement(engagementID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditEngagementMutationResult = NonNullable<
  Awaited<ReturnType<typeof editEngagement>>
>;
export type EditEngagementMutationBody = BodyType<
  NonReadonly<EditEngagementInputBody>
>;
export type EditEngagementMutationError = ErrorType<ErrorModel>;

/**
 * @summary Edit an Engagement
 */
export const useEditEngagement = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editEngagement>>,
      TError,
      {
        engagementID: string;
        data: BodyType<NonReadonly<EditEngagementInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editEngagement>>,
  TError,
  {
    engagementID: string;
    data: BodyType<NonReadonly<EditEngagementInputBody>>;
  },
  TContext
> => {
  const mutationOptions = getEditEngagementMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get AWS accounts for an engagement
 */
export const getEngagementAwsAccounts = (
  engagementID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetAWSAccountsOutputBody>(
    { url: `/engagements/${engagementID}/aws-accounts`, method: "GET", signal },
    options,
  );
};

export const getGetEngagementAwsAccountsQueryKey = (engagementID?: string) => {
  return [`/engagements/${engagementID}/aws-accounts`] as const;
};

export const getGetEngagementAwsAccountsQueryOptions = <
  TData = Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetEngagementAwsAccountsQueryKey(engagementID);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getEngagementAwsAccounts>>
  > = ({ signal }) =>
    getEngagementAwsAccounts(engagementID, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!engagementID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetEngagementAwsAccountsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getEngagementAwsAccounts>>
>;
export type GetEngagementAwsAccountsQueryError = ErrorType<ErrorModel>;

export function useGetEngagementAwsAccounts<
  TData = Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
          TError,
          Awaited<ReturnType<typeof getEngagementAwsAccounts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementAwsAccounts<
  TData = Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
          TError,
          Awaited<ReturnType<typeof getEngagementAwsAccounts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementAwsAccounts<
  TData = Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get AWS accounts for an engagement
 */

export function useGetEngagementAwsAccounts<
  TData = Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAwsAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetEngagementAwsAccountsQueryOptions(
    engagementID,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Azure tenants for an engagement
 */
export const getEngagementAzureTenants = (
  engagementID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetAzureTenantsOutputBody>(
    {
      url: `/engagements/${engagementID}/azure-tenants`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getGetEngagementAzureTenantsQueryKey = (engagementID?: string) => {
  return [`/engagements/${engagementID}/azure-tenants`] as const;
};

export const getGetEngagementAzureTenantsQueryOptions = <
  TData = Awaited<ReturnType<typeof getEngagementAzureTenants>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAzureTenants>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetEngagementAzureTenantsQueryKey(engagementID);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getEngagementAzureTenants>>
  > = ({ signal }) =>
    getEngagementAzureTenants(engagementID, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!engagementID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getEngagementAzureTenants>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetEngagementAzureTenantsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getEngagementAzureTenants>>
>;
export type GetEngagementAzureTenantsQueryError = ErrorType<ErrorModel>;

export function useGetEngagementAzureTenants<
  TData = Awaited<ReturnType<typeof getEngagementAzureTenants>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAzureTenants>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementAzureTenants>>,
          TError,
          Awaited<ReturnType<typeof getEngagementAzureTenants>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementAzureTenants<
  TData = Awaited<ReturnType<typeof getEngagementAzureTenants>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAzureTenants>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementAzureTenants>>,
          TError,
          Awaited<ReturnType<typeof getEngagementAzureTenants>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementAzureTenants<
  TData = Awaited<ReturnType<typeof getEngagementAzureTenants>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAzureTenants>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Azure tenants for an engagement
 */

export function useGetEngagementAzureTenants<
  TData = Awaited<ReturnType<typeof getEngagementAzureTenants>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementAzureTenants>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetEngagementAzureTenantsQueryOptions(
    engagementID,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get all cloud accounts (AWS + Azure) for an engagement
 */
export const getEngagementCloudAccounts = (
  engagementID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetCloudAccountsOutputBody>(
    {
      url: `/engagements/${engagementID}/cloud-accounts`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getGetEngagementCloudAccountsQueryKey = (
  engagementID?: string,
) => {
  return [`/engagements/${engagementID}/cloud-accounts`] as const;
};

export const getGetEngagementCloudAccountsQueryOptions = <
  TData = Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetEngagementCloudAccountsQueryKey(engagementID);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getEngagementCloudAccounts>>
  > = ({ signal }) =>
    getEngagementCloudAccounts(engagementID, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!engagementID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetEngagementCloudAccountsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getEngagementCloudAccounts>>
>;
export type GetEngagementCloudAccountsQueryError = ErrorType<ErrorModel>;

export function useGetEngagementCloudAccounts<
  TData = Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
          TError,
          Awaited<ReturnType<typeof getEngagementCloudAccounts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementCloudAccounts<
  TData = Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
          TError,
          Awaited<ReturnType<typeof getEngagementCloudAccounts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementCloudAccounts<
  TData = Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get all cloud accounts (AWS + Azure) for an engagement
 */

export function useGetEngagementCloudAccounts<
  TData = Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudAccounts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetEngagementCloudAccountsQueryOptions(
    engagementID,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Engagement Cloud Instances
 */
export const getEngagementCloudInstances = (
  engagementID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetEngagementCloudInstancesOutputBody>(
    {
      url: `/engagements/${engagementID}/cloud-instances`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getGetEngagementCloudInstancesQueryKey = (
  engagementID?: string,
) => {
  return [`/engagements/${engagementID}/cloud-instances`] as const;
};

export const getGetEngagementCloudInstancesQueryOptions = <
  TData = Awaited<ReturnType<typeof getEngagementCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudInstances>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetEngagementCloudInstancesQueryKey(engagementID);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getEngagementCloudInstances>>
  > = ({ signal }) =>
    getEngagementCloudInstances(engagementID, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!engagementID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getEngagementCloudInstances>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetEngagementCloudInstancesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getEngagementCloudInstances>>
>;
export type GetEngagementCloudInstancesQueryError = ErrorType<ErrorModel>;

export function useGetEngagementCloudInstances<
  TData = Awaited<ReturnType<typeof getEngagementCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudInstances>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementCloudInstances>>,
          TError,
          Awaited<ReturnType<typeof getEngagementCloudInstances>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementCloudInstances<
  TData = Awaited<ReturnType<typeof getEngagementCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudInstances>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementCloudInstances>>,
          TError,
          Awaited<ReturnType<typeof getEngagementCloudInstances>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementCloudInstances<
  TData = Awaited<ReturnType<typeof getEngagementCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudInstances>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagement Cloud Instances
 */

export function useGetEngagementCloudInstances<
  TData = Awaited<ReturnType<typeof getEngagementCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementCloudInstances>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetEngagementCloudInstancesQueryOptions(
    engagementID,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Engagement Graphs
 */
export const getEngagementGraphs = (
  engagementID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetEngagementGraphsOutputBody>(
    { url: `/engagements/${engagementID}/graphs`, method: "GET", signal },
    options,
  );
};

export const getGetEngagementGraphsQueryKey = (engagementID?: string) => {
  return [`/engagements/${engagementID}/graphs`] as const;
};

export const getGetEngagementGraphsQueryOptions = <
  TData = Awaited<ReturnType<typeof getEngagementGraphs>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraphs>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetEngagementGraphsQueryKey(engagementID);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getEngagementGraphs>>
  > = ({ signal }) => getEngagementGraphs(engagementID, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!engagementID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getEngagementGraphs>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetEngagementGraphsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getEngagementGraphs>>
>;
export type GetEngagementGraphsQueryError = ErrorType<ErrorModel>;

export function useGetEngagementGraphs<
  TData = Awaited<ReturnType<typeof getEngagementGraphs>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraphs>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementGraphs>>,
          TError,
          Awaited<ReturnType<typeof getEngagementGraphs>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementGraphs<
  TData = Awaited<ReturnType<typeof getEngagementGraphs>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraphs>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementGraphs>>,
          TError,
          Awaited<ReturnType<typeof getEngagementGraphs>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementGraphs<
  TData = Awaited<ReturnType<typeof getEngagementGraphs>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraphs>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagement Graphs
 */

export function useGetEngagementGraphs<
  TData = Awaited<ReturnType<typeof getEngagementGraphs>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraphs>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetEngagementGraphsQueryOptions(
    engagementID,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get an Engagement Graph given its Node Group ID
 */
export const getEngagementGraph = (
  engagementID: string,
  nodeGroupID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetEngagementGraphOutputBody>(
    {
      url: `/engagements/${engagementID}/graphs/${nodeGroupID}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getGetEngagementGraphQueryKey = (
  engagementID?: string,
  nodeGroupID?: string,
) => {
  return [`/engagements/${engagementID}/graphs/${nodeGroupID}`] as const;
};

export const getGetEngagementGraphQueryOptions = <
  TData = Awaited<ReturnType<typeof getEngagementGraph>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  nodeGroupID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraph>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetEngagementGraphQueryKey(engagementID, nodeGroupID);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getEngagementGraph>>
  > = ({ signal }) =>
    getEngagementGraph(engagementID, nodeGroupID, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!(engagementID && nodeGroupID),
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getEngagementGraph>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetEngagementGraphQueryResult = NonNullable<
  Awaited<ReturnType<typeof getEngagementGraph>>
>;
export type GetEngagementGraphQueryError = ErrorType<ErrorModel>;

export function useGetEngagementGraph<
  TData = Awaited<ReturnType<typeof getEngagementGraph>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  nodeGroupID: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraph>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementGraph>>,
          TError,
          Awaited<ReturnType<typeof getEngagementGraph>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementGraph<
  TData = Awaited<ReturnType<typeof getEngagementGraph>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  nodeGroupID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraph>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getEngagementGraph>>,
          TError,
          Awaited<ReturnType<typeof getEngagementGraph>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetEngagementGraph<
  TData = Awaited<ReturnType<typeof getEngagementGraph>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  nodeGroupID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraph>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get an Engagement Graph given its Node Group ID
 */

export function useGetEngagementGraph<
  TData = Awaited<ReturnType<typeof getEngagementGraph>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  nodeGroupID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getEngagementGraph>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetEngagementGraphQueryOptions(
    engagementID,
    nodeGroupID,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * Updates engagement status by setting is_active field. Setting to false deactivates the engagement.
 * @summary Update Engagement Status
 */
export const deactivateEngagement = (
  engagementID: string,
  deactivateEngagementInputBody: BodyType<
    NonReadonly<DeactivateEngagementInputBody>
  >,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    {
      url: `/engagements/${engagementID}/status`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: deactivateEngagementInputBody,
    },
    options,
  );
};

export const getDeactivateEngagementMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deactivateEngagement>>,
    TError,
    {
      engagementID: string;
      data: BodyType<NonReadonly<DeactivateEngagementInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deactivateEngagement>>,
  TError,
  {
    engagementID: string;
    data: BodyType<NonReadonly<DeactivateEngagementInputBody>>;
  },
  TContext
> => {
  const mutationKey = ["deactivateEngagement"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deactivateEngagement>>,
    {
      engagementID: string;
      data: BodyType<NonReadonly<DeactivateEngagementInputBody>>;
    }
  > = (props) => {
    const { engagementID, data } = props ?? {};

    return deactivateEngagement(engagementID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeactivateEngagementMutationResult = NonNullable<
  Awaited<ReturnType<typeof deactivateEngagement>>
>;
export type DeactivateEngagementMutationBody = BodyType<
  NonReadonly<DeactivateEngagementInputBody>
>;
export type DeactivateEngagementMutationError = ErrorType<ErrorModel>;

/**
 * @summary Update Engagement Status
 */
export const useDeactivateEngagement = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof deactivateEngagement>>,
      TError,
      {
        engagementID: string;
        data: BodyType<NonReadonly<DeactivateEngagementInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof deactivateEngagement>>,
  TError,
  {
    engagementID: string;
    data: BodyType<NonReadonly<DeactivateEngagementInputBody>>;
  },
  TContext
> => {
  const mutationOptions = getDeactivateEngagementMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get User Assignment Log / engagement
 */
export const getLogAssignmentsEngagementId = (
  engagementID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetUserAssignmentLogsOutputBody>(
    {
      url: `/engagements/${engagementID}/user-assignment-logs`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getGetLogAssignmentsEngagementIdQueryKey = (
  engagementID?: string,
) => {
  return [`/engagements/${engagementID}/user-assignment-logs`] as const;
};

export const getGetLogAssignmentsEngagementIdQueryOptions = <
  TData = Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetLogAssignmentsEngagementIdQueryKey(engagementID);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>
  > = ({ signal }) =>
    getLogAssignmentsEngagementId(engagementID, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!engagementID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetLogAssignmentsEngagementIdQueryResult = NonNullable<
  Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>
>;
export type GetLogAssignmentsEngagementIdQueryError = ErrorType<ErrorModel>;

export function useGetLogAssignmentsEngagementId<
  TData = Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
          TError,
          Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetLogAssignmentsEngagementId<
  TData = Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
          TError,
          Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetLogAssignmentsEngagementId<
  TData = Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get User Assignment Log / engagement
 */

export function useGetLogAssignmentsEngagementId<
  TData = Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
  TError = ErrorType<ErrorModel>,
>(
  engagementID: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getLogAssignmentsEngagementId>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetLogAssignmentsEngagementIdQueryOptions(
    engagementID,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Request creation of AWS account for an engagement
 */
export const postCreateAwsAccount = (
  engagementId: string,
  createAWSAccountInputBody: BodyType<NonReadonly<CreateAWSAccountInputBody>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/engagements/${engagementId}/addAWSAccount`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createAWSAccountInputBody,
      signal,
    },
    options,
  );
};

export const getPostCreateAwsAccountMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postCreateAwsAccount>>,
    TError,
    {
      engagementId: string;
      data: BodyType<NonReadonly<CreateAWSAccountInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postCreateAwsAccount>>,
  TError,
  {
    engagementId: string;
    data: BodyType<NonReadonly<CreateAWSAccountInputBody>>;
  },
  TContext
> => {
  const mutationKey = ["postCreateAwsAccount"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postCreateAwsAccount>>,
    {
      engagementId: string;
      data: BodyType<NonReadonly<CreateAWSAccountInputBody>>;
    }
  > = (props) => {
    const { engagementId, data } = props ?? {};

    return postCreateAwsAccount(engagementId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostCreateAwsAccountMutationResult = NonNullable<
  Awaited<ReturnType<typeof postCreateAwsAccount>>
>;
export type PostCreateAwsAccountMutationBody = BodyType<
  NonReadonly<CreateAWSAccountInputBody>
>;
export type PostCreateAwsAccountMutationError = ErrorType<ErrorModel>;

/**
 * @summary Request creation of AWS account for an engagement
 */
export const usePostCreateAwsAccount = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postCreateAwsAccount>>,
      TError,
      {
        engagementId: string;
        data: BodyType<NonReadonly<CreateAWSAccountInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postCreateAwsAccount>>,
  TError,
  {
    engagementId: string;
    data: BodyType<NonReadonly<CreateAWSAccountInputBody>>;
  },
  TContext
> => {
  const mutationOptions = getPostCreateAwsAccountMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Add an Azure tenant to an engagement
 */
export const postCreateAzureTenant = (
  engagementId: string,
  azureTenantForm: BodyType<NonReadonly<AzureTenantForm>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/engagements/${engagementId}/addAzureTenant`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: azureTenantForm,
      signal,
    },
    options,
  );
};

export const getPostCreateAzureTenantMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postCreateAzureTenant>>,
    TError,
    { engagementId: string; data: BodyType<NonReadonly<AzureTenantForm>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postCreateAzureTenant>>,
  TError,
  { engagementId: string; data: BodyType<NonReadonly<AzureTenantForm>> },
  TContext
> => {
  const mutationKey = ["postCreateAzureTenant"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postCreateAzureTenant>>,
    { engagementId: string; data: BodyType<NonReadonly<AzureTenantForm>> }
  > = (props) => {
    const { engagementId, data } = props ?? {};

    return postCreateAzureTenant(engagementId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostCreateAzureTenantMutationResult = NonNullable<
  Awaited<ReturnType<typeof postCreateAzureTenant>>
>;
export type PostCreateAzureTenantMutationBody = BodyType<
  NonReadonly<AzureTenantForm>
>;
export type PostCreateAzureTenantMutationError = ErrorType<ErrorModel>;

/**
 * @summary Add an Azure tenant to an engagement
 */
export const usePostCreateAzureTenant = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postCreateAzureTenant>>,
      TError,
      { engagementId: string; data: BodyType<NonReadonly<AzureTenantForm>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postCreateAzureTenant>>,
  TError,
  { engagementId: string; data: BodyType<NonReadonly<AzureTenantForm>> },
  TContext
> => {
  const mutationOptions = getPostCreateAzureTenantMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get Inventory
 */
export const getInventory = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInventoryOutputBody>(
    { url: `/inventory`, method: "GET", signal },
    options,
  );
};

export const getGetInventoryQueryKey = () => {
  return [`/inventory`] as const;
};

export const getGetInventoryQueryOptions = <
  TData = Awaited<ReturnType<typeof getInventory>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getInventory>>, TError, TData>
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetInventoryQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getInventory>>> = ({
    signal,
  }) => getInventory(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getInventory>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInventoryQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInventory>>
>;
export type GetInventoryQueryError = ErrorType<ErrorModel>;

export function useGetInventory<
  TData = Awaited<ReturnType<typeof getInventory>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getInventory>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventory>>,
          TError,
          Awaited<ReturnType<typeof getInventory>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventory<
  TData = Awaited<ReturnType<typeof getInventory>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getInventory>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventory>>,
          TError,
          Awaited<ReturnType<typeof getInventory>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventory<
  TData = Awaited<ReturnType<typeof getInventory>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getInventory>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Inventory
 */

export function useGetInventory<
  TData = Awaited<ReturnType<typeof getInventory>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getInventory>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInventoryQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Engagement Cloud Instances
 */
export const getInventoryCloudInstances = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInventoryCloudInstancesOutputBody>(
    { url: `/inventory/cloud-instances`, method: "GET", signal },
    options,
  );
};

export const getGetInventoryCloudInstancesQueryKey = () => {
  return [`/inventory/cloud-instances`] as const;
};

export const getGetInventoryCloudInstancesQueryOptions = <
  TData = Awaited<ReturnType<typeof getInventoryCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getInventoryCloudInstances>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetInventoryCloudInstancesQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getInventoryCloudInstances>>
  > = ({ signal }) => getInventoryCloudInstances(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getInventoryCloudInstances>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInventoryCloudInstancesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInventoryCloudInstances>>
>;
export type GetInventoryCloudInstancesQueryError = ErrorType<ErrorModel>;

export function useGetInventoryCloudInstances<
  TData = Awaited<ReturnType<typeof getInventoryCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryCloudInstances>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryCloudInstances>>,
          TError,
          Awaited<ReturnType<typeof getInventoryCloudInstances>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryCloudInstances<
  TData = Awaited<ReturnType<typeof getInventoryCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryCloudInstances>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryCloudInstances>>,
          TError,
          Awaited<ReturnType<typeof getInventoryCloudInstances>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryCloudInstances<
  TData = Awaited<ReturnType<typeof getInventoryCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryCloudInstances>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagement Cloud Instances
 */

export function useGetInventoryCloudInstances<
  TData = Awaited<ReturnType<typeof getInventoryCloudInstances>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryCloudInstances>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInventoryCloudInstancesQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get all domains with activity history
 */
export const getInventoryDomains = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInventoryDomainsOutputBody>(
    { url: `/inventory/domains`, method: "GET", signal },
    options,
  );
};

export const getGetInventoryDomainsQueryKey = () => {
  return [`/inventory/domains`] as const;
};

export const getGetInventoryDomainsQueryOptions = <
  TData = Awaited<ReturnType<typeof getInventoryDomains>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getInventoryDomains>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetInventoryDomainsQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getInventoryDomains>>
  > = ({ signal }) => getInventoryDomains(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getInventoryDomains>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInventoryDomainsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInventoryDomains>>
>;
export type GetInventoryDomainsQueryError = ErrorType<ErrorModel>;

export function useGetInventoryDomains<
  TData = Awaited<ReturnType<typeof getInventoryDomains>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryDomains>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryDomains>>,
          TError,
          Awaited<ReturnType<typeof getInventoryDomains>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryDomains<
  TData = Awaited<ReturnType<typeof getInventoryDomains>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryDomains>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryDomains>>,
          TError,
          Awaited<ReturnType<typeof getInventoryDomains>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryDomains<
  TData = Awaited<ReturnType<typeof getInventoryDomains>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryDomains>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get all domains with activity history
 */

export function useGetInventoryDomains<
  TData = Awaited<ReturnType<typeof getInventoryDomains>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryDomains>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInventoryDomainsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Import domains from CSV file
 */
export const postInventoryDomainsImportBase64 = (
  importDomainsBase64InputBody: BodyType<
    NonReadonly<ImportDomainsBase64InputBody>
  >,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<ImportDomainsOutputBody>(
    {
      url: `/inventory/domains/import`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: importDomainsBase64InputBody,
      signal,
    },
    options,
  );
};

export const getPostInventoryDomainsImportBase64MutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postInventoryDomainsImportBase64>>,
    TError,
    { data: BodyType<NonReadonly<ImportDomainsBase64InputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postInventoryDomainsImportBase64>>,
  TError,
  { data: BodyType<NonReadonly<ImportDomainsBase64InputBody>> },
  TContext
> => {
  const mutationKey = ["postInventoryDomainsImportBase64"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postInventoryDomainsImportBase64>>,
    { data: BodyType<NonReadonly<ImportDomainsBase64InputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postInventoryDomainsImportBase64(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostInventoryDomainsImportBase64MutationResult = NonNullable<
  Awaited<ReturnType<typeof postInventoryDomainsImportBase64>>
>;
export type PostInventoryDomainsImportBase64MutationBody = BodyType<
  NonReadonly<ImportDomainsBase64InputBody>
>;
export type PostInventoryDomainsImportBase64MutationError =
  ErrorType<ErrorModel>;

/**
 * @summary Import domains from CSV file
 */
export const usePostInventoryDomainsImportBase64 = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postInventoryDomainsImportBase64>>,
      TError,
      { data: BodyType<NonReadonly<ImportDomainsBase64InputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postInventoryDomainsImportBase64>>,
  TError,
  { data: BodyType<NonReadonly<ImportDomainsBase64InputBody>> },
  TContext
> => {
  const mutationOptions =
    getPostInventoryDomainsImportBase64MutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Download Domain Import Template CSV
 */
export const getInventoryDomainsTemplate = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<string>(
    { url: `/inventory/domains/template`, method: "GET", signal },
    options,
  );
};

export const getGetInventoryDomainsTemplateQueryKey = () => {
  return [`/inventory/domains/template`] as const;
};

export const getGetInventoryDomainsTemplateQueryOptions = <
  TData = Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetInventoryDomainsTemplateQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getInventoryDomainsTemplate>>
  > = ({ signal }) => getInventoryDomainsTemplate(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInventoryDomainsTemplateQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInventoryDomainsTemplate>>
>;
export type GetInventoryDomainsTemplateQueryError = ErrorType<ErrorModel>;

export function useGetInventoryDomainsTemplate<
  TData = Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
          TError,
          Awaited<ReturnType<typeof getInventoryDomainsTemplate>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryDomainsTemplate<
  TData = Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
          TError,
          Awaited<ReturnType<typeof getInventoryDomainsTemplate>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryDomainsTemplate<
  TData = Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Download Domain Import Template CSV
 */

export function useGetInventoryDomainsTemplate<
  TData = Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryDomainsTemplate>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInventoryDomainsTemplateQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Engagement Email Addresses
 */
export const getInventoryEmailAddresses = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInventoryEmailAddressesOutputBody>(
    { url: `/inventory/email-addresses`, method: "GET", signal },
    options,
  );
};

export const getGetInventoryEmailAddressesQueryKey = () => {
  return [`/inventory/email-addresses`] as const;
};

export const getGetInventoryEmailAddressesQueryOptions = <
  TData = Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetInventoryEmailAddressesQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getInventoryEmailAddresses>>
  > = ({ signal }) => getInventoryEmailAddresses(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInventoryEmailAddressesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInventoryEmailAddresses>>
>;
export type GetInventoryEmailAddressesQueryError = ErrorType<ErrorModel>;

export function useGetInventoryEmailAddresses<
  TData = Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
          TError,
          Awaited<ReturnType<typeof getInventoryEmailAddresses>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryEmailAddresses<
  TData = Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
          TError,
          Awaited<ReturnType<typeof getInventoryEmailAddresses>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryEmailAddresses<
  TData = Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagement Email Addresses
 */

export function useGetInventoryEmailAddresses<
  TData = Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryEmailAddresses>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInventoryEmailAddressesQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Engagement Hosts
 */
export const getInventoryHosts = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInventoryHostsOutputBody>(
    { url: `/inventory/hosts`, method: "GET", signal },
    options,
  );
};

export const getGetInventoryHostsQueryKey = () => {
  return [`/inventory/hosts`] as const;
};

export const getGetInventoryHostsQueryOptions = <
  TData = Awaited<ReturnType<typeof getInventoryHosts>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getInventoryHosts>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetInventoryHostsQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getInventoryHosts>>
  > = ({ signal }) => getInventoryHosts(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getInventoryHosts>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInventoryHostsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInventoryHosts>>
>;
export type GetInventoryHostsQueryError = ErrorType<ErrorModel>;

export function useGetInventoryHosts<
  TData = Awaited<ReturnType<typeof getInventoryHosts>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryHosts>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryHosts>>,
          TError,
          Awaited<ReturnType<typeof getInventoryHosts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryHosts<
  TData = Awaited<ReturnType<typeof getInventoryHosts>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryHosts>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryHosts>>,
          TError,
          Awaited<ReturnType<typeof getInventoryHosts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryHosts<
  TData = Awaited<ReturnType<typeof getInventoryHosts>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryHosts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagement Hosts
 */

export function useGetInventoryHosts<
  TData = Awaited<ReturnType<typeof getInventoryHosts>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryHosts>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInventoryHostsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Engagement Persons
 */
export const getInventoryPersons = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInventoryPersonsOutputBody>(
    { url: `/inventory/persons`, method: "GET", signal },
    options,
  );
};

export const getGetInventoryPersonsQueryKey = () => {
  return [`/inventory/persons`] as const;
};

export const getGetInventoryPersonsQueryOptions = <
  TData = Awaited<ReturnType<typeof getInventoryPersons>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getInventoryPersons>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetInventoryPersonsQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getInventoryPersons>>
  > = ({ signal }) => getInventoryPersons(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getInventoryPersons>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInventoryPersonsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInventoryPersons>>
>;
export type GetInventoryPersonsQueryError = ErrorType<ErrorModel>;

export function useGetInventoryPersons<
  TData = Awaited<ReturnType<typeof getInventoryPersons>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryPersons>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryPersons>>,
          TError,
          Awaited<ReturnType<typeof getInventoryPersons>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryPersons<
  TData = Awaited<ReturnType<typeof getInventoryPersons>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryPersons>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryPersons>>,
          TError,
          Awaited<ReturnType<typeof getInventoryPersons>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryPersons<
  TData = Awaited<ReturnType<typeof getInventoryPersons>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryPersons>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagement Persons
 */

export function useGetInventoryPersons<
  TData = Awaited<ReturnType<typeof getInventoryPersons>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryPersons>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInventoryPersonsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Engagement Urls
 */
export const getInventoryUrls = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInventoryUrlsOutputBody>(
    { url: `/inventory/urls`, method: "GET", signal },
    options,
  );
};

export const getGetInventoryUrlsQueryKey = () => {
  return [`/inventory/urls`] as const;
};

export const getGetInventoryUrlsQueryOptions = <
  TData = Awaited<ReturnType<typeof getInventoryUrls>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getInventoryUrls>>, TError, TData>
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetInventoryUrlsQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getInventoryUrls>>
  > = ({ signal }) => getInventoryUrls(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getInventoryUrls>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInventoryUrlsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInventoryUrls>>
>;
export type GetInventoryUrlsQueryError = ErrorType<ErrorModel>;

export function useGetInventoryUrls<
  TData = Awaited<ReturnType<typeof getInventoryUrls>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryUrls>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryUrls>>,
          TError,
          Awaited<ReturnType<typeof getInventoryUrls>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryUrls<
  TData = Awaited<ReturnType<typeof getInventoryUrls>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryUrls>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInventoryUrls>>,
          TError,
          Awaited<ReturnType<typeof getInventoryUrls>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInventoryUrls<
  TData = Awaited<ReturnType<typeof getInventoryUrls>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryUrls>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Engagement Urls
 */

export function useGetInventoryUrls<
  TData = Awaited<ReturnType<typeof getInventoryUrls>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInventoryUrls>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInventoryUrlsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Edit Node Group
 */
export const editNodeGroup = (
  nodeGroupID: string,
  editNodeGroupInputBody: BodyType<NonReadonly<EditNodeGroupInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    {
      url: `/node-groups/${nodeGroupID}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editNodeGroupInputBody,
    },
    options,
  );
};

export const getEditNodeGroupMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editNodeGroup>>,
    TError,
    {
      nodeGroupID: string;
      data: BodyType<NonReadonly<EditNodeGroupInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editNodeGroup>>,
  TError,
  { nodeGroupID: string; data: BodyType<NonReadonly<EditNodeGroupInputBody>> },
  TContext
> => {
  const mutationKey = ["editNodeGroup"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editNodeGroup>>,
    { nodeGroupID: string; data: BodyType<NonReadonly<EditNodeGroupInputBody>> }
  > = (props) => {
    const { nodeGroupID, data } = props ?? {};

    return editNodeGroup(nodeGroupID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditNodeGroupMutationResult = NonNullable<
  Awaited<ReturnType<typeof editNodeGroup>>
>;
export type EditNodeGroupMutationBody = BodyType<
  NonReadonly<EditNodeGroupInputBody>
>;
export type EditNodeGroupMutationError = ErrorType<ErrorModel>;

/**
 * @summary Edit Node Group
 */
export const useEditNodeGroup = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editNodeGroup>>,
      TError,
      {
        nodeGroupID: string;
        data: BodyType<NonReadonly<EditNodeGroupInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editNodeGroup>>,
  TError,
  { nodeGroupID: string; data: BodyType<NonReadonly<EditNodeGroupInputBody>> },
  TContext
> => {
  const mutationOptions = getEditNodeGroupMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get Azure instance types (hardcoded for now)
 */
export const getAzureInstanceTypes = (
  region: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInstanceTypesDBOutputBody>(
    {
      url: `/nodes/cloud_instance/azure-instance-types/${region}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getGetAzureInstanceTypesQueryKey = (region?: string) => {
  return [`/nodes/cloud_instance/azure-instance-types/${region}`] as const;
};

export const getGetAzureInstanceTypesQueryOptions = <
  TData = Awaited<ReturnType<typeof getAzureInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAzureInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetAzureInstanceTypesQueryKey(region);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAzureInstanceTypes>>
  > = ({ signal }) => getAzureInstanceTypes(region, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!region,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getAzureInstanceTypes>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAzureInstanceTypesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAzureInstanceTypes>>
>;
export type GetAzureInstanceTypesQueryError = ErrorType<ErrorModel>;

export function useGetAzureInstanceTypes<
  TData = Awaited<ReturnType<typeof getAzureInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAzureInstanceTypes>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAzureInstanceTypes>>,
          TError,
          Awaited<ReturnType<typeof getAzureInstanceTypes>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetAzureInstanceTypes<
  TData = Awaited<ReturnType<typeof getAzureInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAzureInstanceTypes>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAzureInstanceTypes>>,
          TError,
          Awaited<ReturnType<typeof getAzureInstanceTypes>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetAzureInstanceTypes<
  TData = Awaited<ReturnType<typeof getAzureInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAzureInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Azure instance types (hardcoded for now)
 */

export function useGetAzureInstanceTypes<
  TData = Awaited<ReturnType<typeof getAzureInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAzureInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetAzureInstanceTypesQueryOptions(region, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get instance types from the database
 */
export const getInstanceTypes = (
  region: string,
  amiId: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInstanceTypesDBOutputBody>(
    {
      url: `/nodes/cloud_instance/instance-types/${region}/${amiId}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getGetInstanceTypesQueryKey = (
  region?: string,
  amiId?: string,
) => {
  return [`/nodes/cloud_instance/instance-types/${region}/${amiId}`] as const;
};

export const getGetInstanceTypesQueryOptions = <
  TData = Awaited<ReturnType<typeof getInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  amiId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetInstanceTypesQueryKey(region, amiId);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getInstanceTypes>>
  > = ({ signal }) => getInstanceTypes(region, amiId, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!(region && amiId),
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getInstanceTypes>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetInstanceTypesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getInstanceTypes>>
>;
export type GetInstanceTypesQueryError = ErrorType<ErrorModel>;

export function useGetInstanceTypes<
  TData = Awaited<ReturnType<typeof getInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  amiId: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInstanceTypes>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInstanceTypes>>,
          TError,
          Awaited<ReturnType<typeof getInstanceTypes>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInstanceTypes<
  TData = Awaited<ReturnType<typeof getInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  amiId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInstanceTypes>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getInstanceTypes>>,
          TError,
          Awaited<ReturnType<typeof getInstanceTypes>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetInstanceTypes<
  TData = Awaited<ReturnType<typeof getInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  amiId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get instance types from the database
 */

export function useGetInstanceTypes<
  TData = Awaited<ReturnType<typeof getInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  amiId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetInstanceTypesQueryOptions(region, amiId, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get a Cloud Instance Node
 */
export const getNodesCloudInstance = (
  nodeID: string,
  params?: GetNodesCloudInstanceParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetNodeCloudInstanceOutputBody>(
    { url: `/nodes/cloud_instance/${nodeID}`, method: "GET", params, signal },
    options,
  );
};

export const getGetNodesCloudInstanceQueryKey = (
  nodeID?: string,
  params?: GetNodesCloudInstanceParams,
) => {
  return [
    `/nodes/cloud_instance/${nodeID}`,
    ...(params ? [params] : []),
  ] as const;
};

export const getGetNodesCloudInstanceQueryOptions = <
  TData = Awaited<ReturnType<typeof getNodesCloudInstance>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesCloudInstanceParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesCloudInstance>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetNodesCloudInstanceQueryKey(nodeID, params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getNodesCloudInstance>>
  > = ({ signal }) =>
    getNodesCloudInstance(nodeID, params, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!nodeID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getNodesCloudInstance>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetNodesCloudInstanceQueryResult = NonNullable<
  Awaited<ReturnType<typeof getNodesCloudInstance>>
>;
export type GetNodesCloudInstanceQueryError = ErrorType<ErrorModel>;

export function useGetNodesCloudInstance<
  TData = Awaited<ReturnType<typeof getNodesCloudInstance>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params: undefined | GetNodesCloudInstanceParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesCloudInstance>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesCloudInstance>>,
          TError,
          Awaited<ReturnType<typeof getNodesCloudInstance>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesCloudInstance<
  TData = Awaited<ReturnType<typeof getNodesCloudInstance>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesCloudInstanceParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesCloudInstance>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesCloudInstance>>,
          TError,
          Awaited<ReturnType<typeof getNodesCloudInstance>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesCloudInstance<
  TData = Awaited<ReturnType<typeof getNodesCloudInstance>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesCloudInstanceParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesCloudInstance>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get a Cloud Instance Node
 */

export function useGetNodesCloudInstance<
  TData = Awaited<ReturnType<typeof getNodesCloudInstance>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesCloudInstanceParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesCloudInstance>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetNodesCloudInstanceQueryOptions(
    nodeID,
    params,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Edit a Cloud Instance Node
 */
export const editNodesCloudInstance = (
  nodeID: string,
  editNodeTypeCloudInstanceInputBody: BodyType<
    NonReadonly<EditNodeTypeCloudInstanceInputBody>
  >,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<EditNodeTypeCloudInstanceOutputBody>(
    {
      url: `/nodes/cloud_instance/${nodeID}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editNodeTypeCloudInstanceInputBody,
    },
    options,
  );
};

export const getEditNodesCloudInstanceMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editNodesCloudInstance>>,
    TError,
    {
      nodeID: string;
      data: BodyType<NonReadonly<EditNodeTypeCloudInstanceInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editNodesCloudInstance>>,
  TError,
  {
    nodeID: string;
    data: BodyType<NonReadonly<EditNodeTypeCloudInstanceInputBody>>;
  },
  TContext
> => {
  const mutationKey = ["editNodesCloudInstance"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editNodesCloudInstance>>,
    {
      nodeID: string;
      data: BodyType<NonReadonly<EditNodeTypeCloudInstanceInputBody>>;
    }
  > = (props) => {
    const { nodeID, data } = props ?? {};

    return editNodesCloudInstance(nodeID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditNodesCloudInstanceMutationResult = NonNullable<
  Awaited<ReturnType<typeof editNodesCloudInstance>>
>;
export type EditNodesCloudInstanceMutationBody = BodyType<
  NonReadonly<EditNodeTypeCloudInstanceInputBody>
>;
export type EditNodesCloudInstanceMutationError = ErrorType<ErrorModel>;

/**
 * @summary Edit a Cloud Instance Node
 */
export const useEditNodesCloudInstance = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editNodesCloudInstance>>,
      TError,
      {
        nodeID: string;
        data: BodyType<NonReadonly<EditNodeTypeCloudInstanceInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editNodesCloudInstance>>,
  TError,
  {
    nodeID: string;
    data: BodyType<NonReadonly<EditNodeTypeCloudInstanceInputBody>>;
  },
  TContext
> => {
  const mutationOptions = getEditNodesCloudInstanceMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary ReIP an Azure Cloud Instance: stop then start and refresh public IP
 */
export const postAzureNodesCloudInstanceReip = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    {
      url: `/nodes/cloud_instance/${nodeID}/azure/reip`,
      method: "POST",
      signal,
    },
    options,
  );
};

export const getPostAzureNodesCloudInstanceReipMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceReip>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceReip>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postAzureNodesCloudInstanceReip"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceReip>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postAzureNodesCloudInstanceReip(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostAzureNodesCloudInstanceReapiMutationResult = NonNullable<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceReapi>>
>;

export type PostAzureNodesCloudInstanceReapiMutationError =
  ErrorType<ErrorModel>;

/**
 * @summary ReAPI an Azure Cloud Instance: stop then start and refresh public IP
 */
export const usePostAzureNodesCloudInstanceReapi = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postAzureNodesCloudInstanceReapi>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceReapi>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostAzureNodesCloudInstanceReapiMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Restart an Azure Cloud Instance
 */
export const postAzureNodesCloudInstanceRestart = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    {
      url: `/nodes/cloud_instance/${nodeID}/azure/restart`,
      method: "POST",
      signal,
    },
    options,
  );
};

export const getPostAzureNodesCloudInstanceRestartMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceRestart>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceRestart>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postAzureNodesCloudInstanceRestart"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceRestart>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postAzureNodesCloudInstanceRestart(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostAzureNodesCloudInstanceRestartMutationResult = NonNullable<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceRestart>>
>;

export type PostAzureNodesCloudInstanceRestartMutationError =
  ErrorType<ErrorModel>;

/**
 * @summary Restart an Azure Cloud Instance
 */
export const usePostAzureNodesCloudInstanceRestart = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postAzureNodesCloudInstanceRestart>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceRestart>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostAzureNodesCloudInstanceRestartMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Start an Azure Cloud Instance
 */
export const postAzureNodesCloudInstanceStart = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    {
      url: `/nodes/cloud_instance/${nodeID}/azure/start`,
      method: "POST",
      signal,
    },
    options,
  );
};

export const getPostAzureNodesCloudInstanceStartMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceStart>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceStart>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postAzureNodesCloudInstanceStart"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceStart>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postAzureNodesCloudInstanceStart(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostAzureNodesCloudInstanceStartMutationResult = NonNullable<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceStart>>
>;

export type PostAzureNodesCloudInstanceStartMutationError =
  ErrorType<ErrorModel>;

/**
 * @summary Start an Azure Cloud Instance
 */
export const usePostAzureNodesCloudInstanceStart = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postAzureNodesCloudInstanceStart>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceStart>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostAzureNodesCloudInstanceStartMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Stop an Azure Cloud Instance
 */
export const postAzureNodesCloudInstanceStop = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    {
      url: `/nodes/cloud_instance/${nodeID}/azure/stop`,
      method: "POST",
      signal,
    },
    options,
  );
};

export const getPostAzureNodesCloudInstanceStopMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceStop>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceStop>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postAzureNodesCloudInstanceStop"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceStop>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postAzureNodesCloudInstanceStop(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostAzureNodesCloudInstanceStopMutationResult = NonNullable<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceStop>>
>;

export type PostAzureNodesCloudInstanceStopMutationError =
  ErrorType<ErrorModel>;

/**
 * @summary Stop an Azure Cloud Instance
 */
export const usePostAzureNodesCloudInstanceStop = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postAzureNodesCloudInstanceStop>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceStop>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostAzureNodesCloudInstanceStopMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Terminate an Azure Cloud Instance
 */
export const postAzureNodesCloudInstanceTerminate = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    {
      url: `/nodes/cloud_instance/${nodeID}/azure/terminate`,
      method: "POST",
      signal,
    },
    options,
  );
};

export const getPostAzureNodesCloudInstanceTerminateMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceTerminate>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceTerminate>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postAzureNodesCloudInstanceTerminate"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postAzureNodesCloudInstanceTerminate>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postAzureNodesCloudInstanceTerminate(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostAzureNodesCloudInstanceTerminateMutationResult = NonNullable<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceTerminate>>
>;

export type PostAzureNodesCloudInstanceTerminateMutationError =
  ErrorType<ErrorModel>;

/**
 * @summary Terminate an Azure Cloud Instance
 */
export const usePostAzureNodesCloudInstanceTerminate = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postAzureNodesCloudInstanceTerminate>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postAzureNodesCloudInstanceTerminate>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostAzureNodesCloudInstanceTerminateMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary ReAPI an AWS Cloud Instance: stop then start and refresh public IP
 */
export const postNodesCloudInstanceReapi = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    { url: `/nodes/cloud_instance/${nodeID}/reapi`, method: "POST", signal },
    options,
  );
};

export const getPostNodesCloudInstanceReapiMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesCloudInstanceReapi>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesCloudInstanceReapi>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postNodesCloudInstanceReapi"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesCloudInstanceReapi>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postNodesCloudInstanceReapi(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesCloudInstanceReapiMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesCloudInstanceReapi>>
>;

export type PostNodesCloudInstanceReapiMutationError = ErrorType<ErrorModel>;

/**
 * @summary ReAPI an AWS Cloud Instance: stop then start and refresh public IP
 */
export const usePostNodesCloudInstanceReapi = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesCloudInstanceReapi>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesCloudInstanceReapi>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostNodesCloudInstanceReapiMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Reboot an AWS Cloud Instance
 */
export const postNodesCloudInstanceReboot = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    { url: `/nodes/cloud_instance/${nodeID}/reboot`, method: "POST", signal },
    options,
  );
};

export const getPostNodesCloudInstanceRebootMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesCloudInstanceReboot>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesCloudInstanceReboot>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postNodesCloudInstanceReboot"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesCloudInstanceReboot>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postNodesCloudInstanceReboot(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesCloudInstanceRebootMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesCloudInstanceReboot>>
>;

export type PostNodesCloudInstanceRebootMutationError = ErrorType<ErrorModel>;

/**
 * @summary Reboot an AWS Cloud Instance
 */
export const usePostNodesCloudInstanceReboot = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesCloudInstanceReboot>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesCloudInstanceReboot>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostNodesCloudInstanceRebootMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Start an AWS Cloud Instance (only when stopped)
 */
export const postNodesCloudInstanceStart = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    { url: `/nodes/cloud_instance/${nodeID}/start`, method: "POST", signal },
    options,
  );
};

export const getPostNodesCloudInstanceStartMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesCloudInstanceStart>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesCloudInstanceStart>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postNodesCloudInstanceStart"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesCloudInstanceStart>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postNodesCloudInstanceStart(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesCloudInstanceStartMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesCloudInstanceStart>>
>;

export type PostNodesCloudInstanceStartMutationError = ErrorType<ErrorModel>;

/**
 * @summary Start an AWS Cloud Instance (only when stopped)
 */
export const usePostNodesCloudInstanceStart = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesCloudInstanceStart>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesCloudInstanceStart>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostNodesCloudInstanceStartMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Stop an AWS Cloud Instance
 */
export const postNodesCloudInstanceStop = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    { url: `/nodes/cloud_instance/${nodeID}/stop`, method: "POST", signal },
    options,
  );
};

export const getPostNodesCloudInstanceStopMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesCloudInstanceStop>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesCloudInstanceStop>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postNodesCloudInstanceStop"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesCloudInstanceStop>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postNodesCloudInstanceStop(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesCloudInstanceStopMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesCloudInstanceStop>>
>;

export type PostNodesCloudInstanceStopMutationError = ErrorType<ErrorModel>;

/**
 * @summary Stop an AWS Cloud Instance
 */
export const usePostNodesCloudInstanceStop = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesCloudInstanceStop>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesCloudInstanceStop>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions = getPostNodesCloudInstanceStopMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Terminate an AWS Cloud Instance
 */
export const postNodesCloudInstanceTerminate = (
  nodeID: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<AwsActionOutputBody>(
    {
      url: `/nodes/cloud_instance/${nodeID}/terminate`,
      method: "POST",
      signal,
    },
    options,
  );
};

export const getPostNodesCloudInstanceTerminateMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesCloudInstanceTerminate>>,
    TError,
    { nodeID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesCloudInstanceTerminate>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationKey = ["postNodesCloudInstanceTerminate"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesCloudInstanceTerminate>>,
    { nodeID: string }
  > = (props) => {
    const { nodeID } = props ?? {};

    return postNodesCloudInstanceTerminate(nodeID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesCloudInstanceTerminateMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesCloudInstanceTerminate>>
>;

export type PostNodesCloudInstanceTerminateMutationError =
  ErrorType<ErrorModel>;

/**
 * @summary Terminate an AWS Cloud Instance
 */
export const usePostNodesCloudInstanceTerminate = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesCloudInstanceTerminate>>,
      TError,
      { nodeID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesCloudInstanceTerminate>>,
  TError,
  { nodeID: string },
  TContext
> => {
  const mutationOptions =
    getPostNodesCloudInstanceTerminateMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Create a Cloud Instance Node for an Engagement
 */
export const postNodesCloudInstance = (
  nodeCloudInstanceInputBody: BodyType<NonReadonly<NodeCloudInstanceInputBody>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/nodes/cloudinstance`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: nodeCloudInstanceInputBody,
      signal,
    },
    options,
  );
};

export const getPostNodesCloudInstanceMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesCloudInstance>>,
    TError,
    { data: BodyType<NonReadonly<NodeCloudInstanceInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesCloudInstance>>,
  TError,
  { data: BodyType<NonReadonly<NodeCloudInstanceInputBody>> },
  TContext
> => {
  const mutationKey = ["postNodesCloudInstance"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesCloudInstance>>,
    { data: BodyType<NonReadonly<NodeCloudInstanceInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postNodesCloudInstance(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesCloudInstanceMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesCloudInstance>>
>;
export type PostNodesCloudInstanceMutationBody = BodyType<
  NonReadonly<NodeCloudInstanceInputBody>
>;
export type PostNodesCloudInstanceMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create a Cloud Instance Node for an Engagement
 */
export const usePostNodesCloudInstance = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesCloudInstance>>,
      TError,
      { data: BodyType<NonReadonly<NodeCloudInstanceInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesCloudInstance>>,
  TError,
  { data: BodyType<NonReadonly<NodeCloudInstanceInputBody>> },
  TContext
> => {
  const mutationOptions = getPostNodesCloudInstanceMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Create an Email Address Node for an Engagement
 */
export const postNodesEmailAddress = (
  createNodeEmailAddressInputBody: BodyType<
    NonReadonly<CreateNodeEmailAddressInputBody>
  >,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/nodes/email-address`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createNodeEmailAddressInputBody,
      signal,
    },
    options,
  );
};

export const getPostNodesEmailAddressMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesEmailAddress>>,
    TError,
    { data: BodyType<NonReadonly<CreateNodeEmailAddressInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesEmailAddress>>,
  TError,
  { data: BodyType<NonReadonly<CreateNodeEmailAddressInputBody>> },
  TContext
> => {
  const mutationKey = ["postNodesEmailAddress"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesEmailAddress>>,
    { data: BodyType<NonReadonly<CreateNodeEmailAddressInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postNodesEmailAddress(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesEmailAddressMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesEmailAddress>>
>;
export type PostNodesEmailAddressMutationBody = BodyType<
  NonReadonly<CreateNodeEmailAddressInputBody>
>;
export type PostNodesEmailAddressMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create an Email Address Node for an Engagement
 */
export const usePostNodesEmailAddress = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesEmailAddress>>,
      TError,
      { data: BodyType<NonReadonly<CreateNodeEmailAddressInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesEmailAddress>>,
  TError,
  { data: BodyType<NonReadonly<CreateNodeEmailAddressInputBody>> },
  TContext
> => {
  const mutationOptions = getPostNodesEmailAddressMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get an Email Address Node
 */
export const getNodesEmailAddress = (
  nodeID: string,
  params?: GetNodesEmailAddressParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetNodeTypeEmailAddressOutputBody>(
    { url: `/nodes/email_address/${nodeID}`, method: "GET", params, signal },
    options,
  );
};

export const getGetNodesEmailAddressQueryKey = (
  nodeID?: string,
  params?: GetNodesEmailAddressParams,
) => {
  return [
    `/nodes/email_address/${nodeID}`,
    ...(params ? [params] : []),
  ] as const;
};

export const getGetNodesEmailAddressQueryOptions = <
  TData = Awaited<ReturnType<typeof getNodesEmailAddress>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesEmailAddressParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesEmailAddress>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetNodesEmailAddressQueryKey(nodeID, params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getNodesEmailAddress>>
  > = ({ signal }) =>
    getNodesEmailAddress(nodeID, params, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!nodeID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getNodesEmailAddress>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetNodesEmailAddressQueryResult = NonNullable<
  Awaited<ReturnType<typeof getNodesEmailAddress>>
>;
export type GetNodesEmailAddressQueryError = ErrorType<ErrorModel>;

export function useGetNodesEmailAddress<
  TData = Awaited<ReturnType<typeof getNodesEmailAddress>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params: undefined | GetNodesEmailAddressParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesEmailAddress>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesEmailAddress>>,
          TError,
          Awaited<ReturnType<typeof getNodesEmailAddress>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesEmailAddress<
  TData = Awaited<ReturnType<typeof getNodesEmailAddress>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesEmailAddressParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesEmailAddress>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesEmailAddress>>,
          TError,
          Awaited<ReturnType<typeof getNodesEmailAddress>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesEmailAddress<
  TData = Awaited<ReturnType<typeof getNodesEmailAddress>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesEmailAddressParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesEmailAddress>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get an Email Address Node
 */

export function useGetNodesEmailAddress<
  TData = Awaited<ReturnType<typeof getNodesEmailAddress>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesEmailAddressParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getNodesEmailAddress>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetNodesEmailAddressQueryOptions(
    nodeID,
    params,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Edit a Email Address Node
 */
export const editNodesEmailAddress = (
  nodeID: string,
  editNodeTypeEmailAddressInputBody: BodyType<
    NonReadonly<EditNodeTypeEmailAddressInputBody>
  >,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<EditNodeTypeEmailAddressOutputBody>(
    {
      url: `/nodes/email_address/${nodeID}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editNodeTypeEmailAddressInputBody,
    },
    options,
  );
};

export const getEditNodesEmailAddressMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editNodesEmailAddress>>,
    TError,
    {
      nodeID: string;
      data: BodyType<NonReadonly<EditNodeTypeEmailAddressInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editNodesEmailAddress>>,
  TError,
  {
    nodeID: string;
    data: BodyType<NonReadonly<EditNodeTypeEmailAddressInputBody>>;
  },
  TContext
> => {
  const mutationKey = ["editNodesEmailAddress"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editNodesEmailAddress>>,
    {
      nodeID: string;
      data: BodyType<NonReadonly<EditNodeTypeEmailAddressInputBody>>;
    }
  > = (props) => {
    const { nodeID, data } = props ?? {};

    return editNodesEmailAddress(nodeID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditNodesEmailAddressMutationResult = NonNullable<
  Awaited<ReturnType<typeof editNodesEmailAddress>>
>;
export type EditNodesEmailAddressMutationBody = BodyType<
  NonReadonly<EditNodeTypeEmailAddressInputBody>
>;
export type EditNodesEmailAddressMutationError = ErrorType<ErrorModel>;

/**
 * @summary Edit a Email Address Node
 */
export const useEditNodesEmailAddress = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editNodesEmailAddress>>,
      TError,
      {
        nodeID: string;
        data: BodyType<NonReadonly<EditNodeTypeEmailAddressInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editNodesEmailAddress>>,
  TError,
  {
    nodeID: string;
    data: BodyType<NonReadonly<EditNodeTypeEmailAddressInputBody>>;
  },
  TContext
> => {
  const mutationOptions = getEditNodesEmailAddressMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Create a Host Node for an Engagement
 */
export const postNodesHost = (
  createNodeTypeHostInputBody: BodyType<
    NonReadonly<CreateNodeTypeHostInputBody>
  >,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/nodes/host`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createNodeTypeHostInputBody,
      signal,
    },
    options,
  );
};

export const getPostNodesHostMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesHost>>,
    TError,
    { data: BodyType<NonReadonly<CreateNodeTypeHostInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesHost>>,
  TError,
  { data: BodyType<NonReadonly<CreateNodeTypeHostInputBody>> },
  TContext
> => {
  const mutationKey = ["postNodesHost"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesHost>>,
    { data: BodyType<NonReadonly<CreateNodeTypeHostInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postNodesHost(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesHostMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesHost>>
>;
export type PostNodesHostMutationBody = BodyType<
  NonReadonly<CreateNodeTypeHostInputBody>
>;
export type PostNodesHostMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create a Host Node for an Engagement
 */
export const usePostNodesHost = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesHost>>,
      TError,
      { data: BodyType<NonReadonly<CreateNodeTypeHostInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesHost>>,
  TError,
  { data: BodyType<NonReadonly<CreateNodeTypeHostInputBody>> },
  TContext
> => {
  const mutationOptions = getPostNodesHostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get a Host Node
 */
export const getNodesHost = (
  nodeID: string,
  params?: GetNodesHostParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetNodeTypeHostOutputBody>(
    { url: `/nodes/host/${nodeID}`, method: "GET", params, signal },
    options,
  );
};

export const getGetNodesHostQueryKey = (
  nodeID?: string,
  params?: GetNodesHostParams,
) => {
  return [`/nodes/host/${nodeID}`, ...(params ? [params] : [])] as const;
};

export const getGetNodesHostQueryOptions = <
  TData = Awaited<ReturnType<typeof getNodesHost>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesHostParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesHost>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetNodesHostQueryKey(nodeID, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getNodesHost>>> = ({
    signal,
  }) => getNodesHost(nodeID, params, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!nodeID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getNodesHost>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetNodesHostQueryResult = NonNullable<
  Awaited<ReturnType<typeof getNodesHost>>
>;
export type GetNodesHostQueryError = ErrorType<ErrorModel>;

export function useGetNodesHost<
  TData = Awaited<ReturnType<typeof getNodesHost>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params: undefined | GetNodesHostParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesHost>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesHost>>,
          TError,
          Awaited<ReturnType<typeof getNodesHost>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesHost<
  TData = Awaited<ReturnType<typeof getNodesHost>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesHostParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesHost>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesHost>>,
          TError,
          Awaited<ReturnType<typeof getNodesHost>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesHost<
  TData = Awaited<ReturnType<typeof getNodesHost>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesHostParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesHost>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get a Host Node
 */

export function useGetNodesHost<
  TData = Awaited<ReturnType<typeof getNodesHost>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesHostParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesHost>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetNodesHostQueryOptions(nodeID, params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Update a Host Node
 */
export const editNodesHost = (
  nodeID: string,
  editNodeTypeHostInputBody: BodyType<NonReadonly<EditNodeTypeHostInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    {
      url: `/nodes/host/${nodeID}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editNodeTypeHostInputBody,
    },
    options,
  );
};

export const getEditNodesHostMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editNodesHost>>,
    TError,
    { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeHostInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editNodesHost>>,
  TError,
  { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeHostInputBody>> },
  TContext
> => {
  const mutationKey = ["editNodesHost"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editNodesHost>>,
    { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeHostInputBody>> }
  > = (props) => {
    const { nodeID, data } = props ?? {};

    return editNodesHost(nodeID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditNodesHostMutationResult = NonNullable<
  Awaited<ReturnType<typeof editNodesHost>>
>;
export type EditNodesHostMutationBody = BodyType<
  NonReadonly<EditNodeTypeHostInputBody>
>;
export type EditNodesHostMutationError = ErrorType<ErrorModel>;

/**
 * @summary Update a Host Node
 */
export const useEditNodesHost = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editNodesHost>>,
      TError,
      {
        nodeID: string;
        data: BodyType<NonReadonly<EditNodeTypeHostInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editNodesHost>>,
  TError,
  { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeHostInputBody>> },
  TContext
> => {
  const mutationOptions = getEditNodesHostMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Create an Person Node for an Engagement
 */
export const postNodesPerson = (
  createNodeTypePersonInputBody: BodyType<
    NonReadonly<CreateNodeTypePersonInputBody>
  >,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/nodes/person`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createNodeTypePersonInputBody,
      signal,
    },
    options,
  );
};

export const getPostNodesPersonMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesPerson>>,
    TError,
    { data: BodyType<NonReadonly<CreateNodeTypePersonInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesPerson>>,
  TError,
  { data: BodyType<NonReadonly<CreateNodeTypePersonInputBody>> },
  TContext
> => {
  const mutationKey = ["postNodesPerson"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesPerson>>,
    { data: BodyType<NonReadonly<CreateNodeTypePersonInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postNodesPerson(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesPersonMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesPerson>>
>;
export type PostNodesPersonMutationBody = BodyType<
  NonReadonly<CreateNodeTypePersonInputBody>
>;
export type PostNodesPersonMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create an Person Node for an Engagement
 */
export const usePostNodesPerson = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesPerson>>,
      TError,
      { data: BodyType<NonReadonly<CreateNodeTypePersonInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesPerson>>,
  TError,
  { data: BodyType<NonReadonly<CreateNodeTypePersonInputBody>> },
  TContext
> => {
  const mutationOptions = getPostNodesPersonMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get a Person Node
 */
export const getNodesPerson = (
  nodeID: string,
  params?: GetNodesPersonParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetNodeTypePersonOutputBody>(
    { url: `/nodes/person/${nodeID}`, method: "GET", params, signal },
    options,
  );
};

export const getGetNodesPersonQueryKey = (
  nodeID?: string,
  params?: GetNodesPersonParams,
) => {
  return [`/nodes/person/${nodeID}`, ...(params ? [params] : [])] as const;
};

export const getGetNodesPersonQueryOptions = <
  TData = Awaited<ReturnType<typeof getNodesPerson>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesPersonParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesPerson>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetNodesPersonQueryKey(nodeID, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getNodesPerson>>> = ({
    signal,
  }) => getNodesPerson(nodeID, params, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!nodeID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getNodesPerson>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetNodesPersonQueryResult = NonNullable<
  Awaited<ReturnType<typeof getNodesPerson>>
>;
export type GetNodesPersonQueryError = ErrorType<ErrorModel>;

export function useGetNodesPerson<
  TData = Awaited<ReturnType<typeof getNodesPerson>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params: undefined | GetNodesPersonParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesPerson>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesPerson>>,
          TError,
          Awaited<ReturnType<typeof getNodesPerson>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesPerson<
  TData = Awaited<ReturnType<typeof getNodesPerson>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesPersonParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesPerson>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesPerson>>,
          TError,
          Awaited<ReturnType<typeof getNodesPerson>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesPerson<
  TData = Awaited<ReturnType<typeof getNodesPerson>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesPersonParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesPerson>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get a Person Node
 */

export function useGetNodesPerson<
  TData = Awaited<ReturnType<typeof getNodesPerson>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesPersonParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesPerson>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetNodesPersonQueryOptions(nodeID, params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Update a Person Node for an Engagement
 */
export const editNodesPerson = (
  nodeID: string,
  editNodeTypePersonInputBody: BodyType<
    NonReadonly<EditNodeTypePersonInputBody>
  >,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    {
      url: `/nodes/person/${nodeID}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editNodeTypePersonInputBody,
    },
    options,
  );
};

export const getEditNodesPersonMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editNodesPerson>>,
    TError,
    {
      nodeID: string;
      data: BodyType<NonReadonly<EditNodeTypePersonInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editNodesPerson>>,
  TError,
  { nodeID: string; data: BodyType<NonReadonly<EditNodeTypePersonInputBody>> },
  TContext
> => {
  const mutationKey = ["editNodesPerson"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editNodesPerson>>,
    { nodeID: string; data: BodyType<NonReadonly<EditNodeTypePersonInputBody>> }
  > = (props) => {
    const { nodeID, data } = props ?? {};

    return editNodesPerson(nodeID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditNodesPersonMutationResult = NonNullable<
  Awaited<ReturnType<typeof editNodesPerson>>
>;
export type EditNodesPersonMutationBody = BodyType<
  NonReadonly<EditNodeTypePersonInputBody>
>;
export type EditNodesPersonMutationError = ErrorType<ErrorModel>;

/**
 * @summary Update a Person Node for an Engagement
 */
export const useEditNodesPerson = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editNodesPerson>>,
      TError,
      {
        nodeID: string;
        data: BodyType<NonReadonly<EditNodeTypePersonInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editNodesPerson>>,
  TError,
  { nodeID: string; data: BodyType<NonReadonly<EditNodeTypePersonInputBody>> },
  TContext
> => {
  const mutationOptions = getEditNodesPersonMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Create a relationship between two Nodes for an Engagement
 */
export const postNodesRelationship = (
  nodeRelationshipInputBody: BodyType<NonReadonly<NodeRelationshipInputBody>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/nodes/relationship`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: nodeRelationshipInputBody,
      signal,
    },
    options,
  );
};

export const getPostNodesRelationshipMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesRelationship>>,
    TError,
    { data: BodyType<NonReadonly<NodeRelationshipInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesRelationship>>,
  TError,
  { data: BodyType<NonReadonly<NodeRelationshipInputBody>> },
  TContext
> => {
  const mutationKey = ["postNodesRelationship"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesRelationship>>,
    { data: BodyType<NonReadonly<NodeRelationshipInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postNodesRelationship(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesRelationshipMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesRelationship>>
>;
export type PostNodesRelationshipMutationBody = BodyType<
  NonReadonly<NodeRelationshipInputBody>
>;
export type PostNodesRelationshipMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create a relationship between two Nodes for an Engagement
 */
export const usePostNodesRelationship = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesRelationship>>,
      TError,
      { data: BodyType<NonReadonly<NodeRelationshipInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesRelationship>>,
  TError,
  { data: BodyType<NonReadonly<NodeRelationshipInputBody>> },
  TContext
> => {
  const mutationOptions = getPostNodesRelationshipMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Create a URL Node for an Engagement
 */
export const postNodesUrl = (
  createNodeTypeUrlInputBody: BodyType<NonReadonly<CreateNodeTypeUrlInputBody>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<CreateNodeTypeUrlOutputBody>(
    {
      url: `/nodes/url`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createNodeTypeUrlInputBody,
      signal,
    },
    options,
  );
};

export const getPostNodesUrlMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postNodesUrl>>,
    TError,
    { data: BodyType<NonReadonly<CreateNodeTypeUrlInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postNodesUrl>>,
  TError,
  { data: BodyType<NonReadonly<CreateNodeTypeUrlInputBody>> },
  TContext
> => {
  const mutationKey = ["postNodesUrl"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postNodesUrl>>,
    { data: BodyType<NonReadonly<CreateNodeTypeUrlInputBody>> }
  > = (props) => {
    const { data } = props ?? {};

    return postNodesUrl(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostNodesUrlMutationResult = NonNullable<
  Awaited<ReturnType<typeof postNodesUrl>>
>;
export type PostNodesUrlMutationBody = BodyType<
  NonReadonly<CreateNodeTypeUrlInputBody>
>;
export type PostNodesUrlMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create a URL Node for an Engagement
 */
export const usePostNodesUrl = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postNodesUrl>>,
      TError,
      { data: BodyType<NonReadonly<CreateNodeTypeUrlInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postNodesUrl>>,
  TError,
  { data: BodyType<NonReadonly<CreateNodeTypeUrlInputBody>> },
  TContext
> => {
  const mutationOptions = getPostNodesUrlMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get a URL Node
 */
export const getNodesUrl = (
  nodeID: string,
  params?: GetNodesUrlParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetNodeTypeUrlOutputBody>(
    { url: `/nodes/url/${nodeID}`, method: "GET", params, signal },
    options,
  );
};

export const getGetNodesUrlQueryKey = (
  nodeID?: string,
  params?: GetNodesUrlParams,
) => {
  return [`/nodes/url/${nodeID}`, ...(params ? [params] : [])] as const;
};

export const getGetNodesUrlQueryOptions = <
  TData = Awaited<ReturnType<typeof getNodesUrl>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesUrlParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesUrl>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetNodesUrlQueryKey(nodeID, params);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getNodesUrl>>> = ({
    signal,
  }) => getNodesUrl(nodeID, params, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!nodeID,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getNodesUrl>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetNodesUrlQueryResult = NonNullable<
  Awaited<ReturnType<typeof getNodesUrl>>
>;
export type GetNodesUrlQueryError = ErrorType<ErrorModel>;

export function useGetNodesUrl<
  TData = Awaited<ReturnType<typeof getNodesUrl>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params: undefined | GetNodesUrlParams,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesUrl>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesUrl>>,
          TError,
          Awaited<ReturnType<typeof getNodesUrl>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesUrl<
  TData = Awaited<ReturnType<typeof getNodesUrl>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesUrlParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesUrl>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getNodesUrl>>,
          TError,
          Awaited<ReturnType<typeof getNodesUrl>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetNodesUrl<
  TData = Awaited<ReturnType<typeof getNodesUrl>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesUrlParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesUrl>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get a URL Node
 */

export function useGetNodesUrl<
  TData = Awaited<ReturnType<typeof getNodesUrl>>,
  TError = ErrorType<ErrorModel>,
>(
  nodeID: string,
  params?: GetNodesUrlParams,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getNodesUrl>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetNodesUrlQueryOptions(nodeID, params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Update a Url Node for an Engagement
 */
export const editNodesUrl = (
  nodeID: string,
  editNodeTypeUrlInputBody: BodyType<NonReadonly<EditNodeTypeUrlInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<EditNodeTypeUrlOutputBody>(
    {
      url: `/nodes/url/${nodeID}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editNodeTypeUrlInputBody,
    },
    options,
  );
};

export const getEditNodesUrlMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editNodesUrl>>,
    TError,
    { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeUrlInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editNodesUrl>>,
  TError,
  { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeUrlInputBody>> },
  TContext
> => {
  const mutationKey = ["editNodesUrl"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editNodesUrl>>,
    { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeUrlInputBody>> }
  > = (props) => {
    const { nodeID, data } = props ?? {};

    return editNodesUrl(nodeID, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditNodesUrlMutationResult = NonNullable<
  Awaited<ReturnType<typeof editNodesUrl>>
>;
export type EditNodesUrlMutationBody = BodyType<
  NonReadonly<EditNodeTypeUrlInputBody>
>;
export type EditNodesUrlMutationError = ErrorType<ErrorModel>;

/**
 * @summary Update a Url Node for an Engagement
 */
export const useEditNodesUrl = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editNodesUrl>>,
      TError,
      { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeUrlInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editNodesUrl>>,
  TError,
  { nodeID: string; data: BodyType<NonReadonly<EditNodeTypeUrlInputBody>> },
  TContext
> => {
  const mutationOptions = getEditNodesUrlMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Delete Node
 */
export const deleteNode = (
  nodeId: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    { url: `/nodes/${nodeId}`, method: "DELETE" },
    options,
  );
};

export const getDeleteNodeMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteNode>>,
    TError,
    { nodeId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteNode>>,
  TError,
  { nodeId: string },
  TContext
> => {
  const mutationKey = ["deleteNode"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteNode>>,
    { nodeId: string }
  > = (props) => {
    const { nodeId } = props ?? {};

    return deleteNode(nodeId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteNodeMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteNode>>
>;

export type DeleteNodeMutationError = ErrorType<ErrorModel>;

/**
 * @summary Delete Node
 */
export const useDeleteNode = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof deleteNode>>,
      TError,
      { nodeId: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof deleteNode>>,
  TError,
  { nodeId: string },
  TContext
> => {
  const mutationOptions = getDeleteNodeMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Delete a relationship between two Nodes for an Engagement
 */
export const deleteNodeRelationship = (
  sourceID: string,
  targetID: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    { url: `/nodes/${sourceID}/relationships/${targetID}`, method: "DELETE" },
    options,
  );
};

export const getDeleteNodeRelationshipMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteNodeRelationship>>,
    TError,
    { sourceID: string; targetID: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteNodeRelationship>>,
  TError,
  { sourceID: string; targetID: string },
  TContext
> => {
  const mutationKey = ["deleteNodeRelationship"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteNodeRelationship>>,
    { sourceID: string; targetID: string }
  > = (props) => {
    const { sourceID, targetID } = props ?? {};

    return deleteNodeRelationship(sourceID, targetID, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteNodeRelationshipMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteNodeRelationship>>
>;

export type DeleteNodeRelationshipMutationError = ErrorType<ErrorModel>;

/**
 * @summary Delete a relationship between two Nodes for an Engagement
 */
export const useDeleteNodeRelationship = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof deleteNodeRelationship>>,
      TError,
      { sourceID: string; targetID: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof deleteNodeRelationship>>,
  TError,
  { sourceID: string; targetID: string },
  TContext
> => {
  const mutationOptions = getDeleteNodeRelationshipMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get AWS AMIs for a region
 */
export const getProvidersAwsAmis = (
  region: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetAmisOutputBody>(
    { url: `/providers/aws/amis/${region}`, method: "GET", signal },
    options,
  );
};

export const getGetProvidersAwsAmisQueryKey = (region?: string) => {
  return [`/providers/aws/amis/${region}`] as const;
};

export const getGetProvidersAwsAmisQueryOptions = <
  TData = Awaited<ReturnType<typeof getProvidersAwsAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsAmis>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetProvidersAwsAmisQueryKey(region);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getProvidersAwsAmis>>
  > = ({ signal }) => getProvidersAwsAmis(region, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!region,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getProvidersAwsAmis>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProvidersAwsAmisQueryResult = NonNullable<
  Awaited<ReturnType<typeof getProvidersAwsAmis>>
>;
export type GetProvidersAwsAmisQueryError = ErrorType<ErrorModel>;

export function useGetProvidersAwsAmis<
  TData = Awaited<ReturnType<typeof getProvidersAwsAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsAmis>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getProvidersAwsAmis>>,
          TError,
          Awaited<ReturnType<typeof getProvidersAwsAmis>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetProvidersAwsAmis<
  TData = Awaited<ReturnType<typeof getProvidersAwsAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsAmis>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getProvidersAwsAmis>>,
          TError,
          Awaited<ReturnType<typeof getProvidersAwsAmis>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetProvidersAwsAmis<
  TData = Awaited<ReturnType<typeof getProvidersAwsAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsAmis>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get AWS AMIs for a region
 */

export function useGetProvidersAwsAmis<
  TData = Awaited<ReturnType<typeof getProvidersAwsAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsAmis>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetProvidersAwsAmisQueryOptions(region, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get available AWS Regions
 */
export const getProvidersAwsRegions = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetRegionsOutputBody>(
    { url: `/providers/aws/regions`, method: "GET", signal },
    options,
  );
};

export const getGetProvidersAwsRegionsQueryKey = () => {
  return [`/providers/aws/regions`] as const;
};

export const getGetProvidersAwsRegionsQueryOptions = <
  TData = Awaited<ReturnType<typeof getProvidersAwsRegions>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getProvidersAwsRegions>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetProvidersAwsRegionsQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getProvidersAwsRegions>>
  > = ({ signal }) => getProvidersAwsRegions(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getProvidersAwsRegions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProvidersAwsRegionsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getProvidersAwsRegions>>
>;
export type GetProvidersAwsRegionsQueryError = ErrorType<ErrorModel>;

export function useGetProvidersAwsRegions<
  TData = Awaited<ReturnType<typeof getProvidersAwsRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsRegions>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getProvidersAwsRegions>>,
          TError,
          Awaited<ReturnType<typeof getProvidersAwsRegions>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetProvidersAwsRegions<
  TData = Awaited<ReturnType<typeof getProvidersAwsRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsRegions>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getProvidersAwsRegions>>,
          TError,
          Awaited<ReturnType<typeof getProvidersAwsRegions>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetProvidersAwsRegions<
  TData = Awaited<ReturnType<typeof getProvidersAwsRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsRegions>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get available AWS Regions
 */

export function useGetProvidersAwsRegions<
  TData = Awaited<ReturnType<typeof getProvidersAwsRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAwsRegions>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetProvidersAwsRegionsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Azure AMIs for a region
 */
export const getProvidersAzureAmis = (
  tenantID: string,
  region: string,
  instanceType: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetAzureAmisOutputBody>(
    {
      url: `/providers/azure/amis/${tenantID}/${region}/${instanceType}`,
      method: "GET",
      signal,
    },
    options,
  );
};

export const getGetProvidersAzureAmisQueryKey = (
  tenantID?: string,
  region?: string,
  instanceType?: string,
) => {
  return [
    `/providers/azure/amis/${tenantID}/${region}/${instanceType}`,
  ] as const;
};

export const getGetProvidersAzureAmisQueryOptions = <
  TData = Awaited<ReturnType<typeof getProvidersAzureAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  tenantID: string,
  region: string,
  instanceType: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureAmis>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ??
    getGetProvidersAzureAmisQueryKey(tenantID, region, instanceType);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getProvidersAzureAmis>>
  > = ({ signal }) =>
    getProvidersAzureAmis(
      tenantID,
      region,
      instanceType,
      requestOptions,
      signal,
    );

  return {
    queryKey,
    queryFn,
    enabled: !!(tenantID && region && instanceType),
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getProvidersAzureAmis>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProvidersAzureAmisQueryResult = NonNullable<
  Awaited<ReturnType<typeof getProvidersAzureAmis>>
>;
export type GetProvidersAzureAmisQueryError = ErrorType<ErrorModel>;

export function useGetProvidersAzureAmis<
  TData = Awaited<ReturnType<typeof getProvidersAzureAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  tenantID: string,
  region: string,
  instanceType: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureAmis>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getProvidersAzureAmis>>,
          TError,
          Awaited<ReturnType<typeof getProvidersAzureAmis>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetProvidersAzureAmis<
  TData = Awaited<ReturnType<typeof getProvidersAzureAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  tenantID: string,
  region: string,
  instanceType: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureAmis>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getProvidersAzureAmis>>,
          TError,
          Awaited<ReturnType<typeof getProvidersAzureAmis>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetProvidersAzureAmis<
  TData = Awaited<ReturnType<typeof getProvidersAzureAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  tenantID: string,
  region: string,
  instanceType: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureAmis>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Azure AMIs for a region
 */

export function useGetProvidersAzureAmis<
  TData = Awaited<ReturnType<typeof getProvidersAzureAmis>>,
  TError = ErrorType<ErrorModel>,
>(
  tenantID: string,
  region: string,
  instanceType: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureAmis>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetProvidersAzureAmisQueryOptions(
    tenantID,
    region,
    instanceType,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get available Azure VM Regions
 */
export const getProvidersAzureRegions = (
  params?: GetProvidersAzureRegionsParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetRegionsOutputBody>(
    { url: `/providers/azure/regions`, method: "GET", params, signal },
    options,
  );
};

export const getGetProvidersAzureRegionsQueryKey = (
  params?: GetProvidersAzureRegionsParams,
) => {
  return [`/providers/azure/regions`, ...(params ? [params] : [])] as const;
};

export const getGetProvidersAzureRegionsQueryOptions = <
  TData = Awaited<ReturnType<typeof getProvidersAzureRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  params?: GetProvidersAzureRegionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureRegions>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetProvidersAzureRegionsQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getProvidersAzureRegions>>
  > = ({ signal }) => getProvidersAzureRegions(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getProvidersAzureRegions>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetProvidersAzureRegionsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getProvidersAzureRegions>>
>;
export type GetProvidersAzureRegionsQueryError = ErrorType<ErrorModel>;

export function useGetProvidersAzureRegions<
  TData = Awaited<ReturnType<typeof getProvidersAzureRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  params: undefined | GetProvidersAzureRegionsParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureRegions>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getProvidersAzureRegions>>,
          TError,
          Awaited<ReturnType<typeof getProvidersAzureRegions>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetProvidersAzureRegions<
  TData = Awaited<ReturnType<typeof getProvidersAzureRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  params?: GetProvidersAzureRegionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureRegions>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getProvidersAzureRegions>>,
          TError,
          Awaited<ReturnType<typeof getProvidersAzureRegions>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetProvidersAzureRegions<
  TData = Awaited<ReturnType<typeof getProvidersAzureRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  params?: GetProvidersAzureRegionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureRegions>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get available Azure VM Regions
 */

export function useGetProvidersAzureRegions<
  TData = Awaited<ReturnType<typeof getProvidersAzureRegions>>,
  TError = ErrorType<ErrorModel>,
>(
  params?: GetProvidersAzureRegionsParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getProvidersAzureRegions>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetProvidersAzureRegionsQueryOptions(params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Set instance types for multiple size and priority combinations
 */
export const setInstanceType = (
  setInstanceTypeRequest: BodyType<NonReadonly<SetInstanceTypeRequest>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<null>(
    {
      url: `/security/set-instance-types`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: setInstanceTypeRequest,
      signal,
    },
    options,
  );
};

export const getSetInstanceTypeMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof setInstanceType>>,
    TError,
    { data: BodyType<NonReadonly<SetInstanceTypeRequest>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof setInstanceType>>,
  TError,
  { data: BodyType<NonReadonly<SetInstanceTypeRequest>> },
  TContext
> => {
  const mutationKey = ["setInstanceType"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof setInstanceType>>,
    { data: BodyType<NonReadonly<SetInstanceTypeRequest>> }
  > = (props) => {
    const { data } = props ?? {};

    return setInstanceType(data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SetInstanceTypeMutationResult = NonNullable<
  Awaited<ReturnType<typeof setInstanceType>>
>;
export type SetInstanceTypeMutationBody = BodyType<
  NonReadonly<SetInstanceTypeRequest>
>;
export type SetInstanceTypeMutationError = ErrorType<ErrorModel>;

/**
 * @summary Set instance types for multiple size and priority combinations
 */
export const useSetInstanceType = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof setInstanceType>>,
      TError,
      { data: BodyType<NonReadonly<SetInstanceTypeRequest>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof setInstanceType>>,
  TError,
  { data: BodyType<NonReadonly<SetInstanceTypeRequest>> },
  TContext
> => {
  const mutationOptions = getSetInstanceTypeMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get AWS instance types for a region
 */
export const getAwsInstanceTypes = (
  region: string,
  params?: GetAwsInstanceTypesParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetInstanceTypesOutputBody>(
    {
      url: `/security/set-instance-types/aws/instance-types/${region}`,
      method: "GET",
      params,
      signal,
    },
    options,
  );
};

export const getGetAwsInstanceTypesQueryKey = (
  region?: string,
  params?: GetAwsInstanceTypesParams,
) => {
  return [
    `/security/set-instance-types/aws/instance-types/${region}`,
    ...(params ? [params] : []),
  ] as const;
};

export const getGetAwsInstanceTypesQueryOptions = <
  TData = Awaited<ReturnType<typeof getAwsInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  params?: GetAwsInstanceTypesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAwsInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetAwsInstanceTypesQueryKey(region, params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAwsInstanceTypes>>
  > = ({ signal }) =>
    getAwsInstanceTypes(region, params, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!region,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getAwsInstanceTypes>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAwsInstanceTypesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAwsInstanceTypes>>
>;
export type GetAwsInstanceTypesQueryError = ErrorType<ErrorModel>;

export function useGetAwsInstanceTypes<
  TData = Awaited<ReturnType<typeof getAwsInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  params: undefined | GetAwsInstanceTypesParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAwsInstanceTypes>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAwsInstanceTypes>>,
          TError,
          Awaited<ReturnType<typeof getAwsInstanceTypes>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetAwsInstanceTypes<
  TData = Awaited<ReturnType<typeof getAwsInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  params?: GetAwsInstanceTypesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAwsInstanceTypes>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAwsInstanceTypes>>,
          TError,
          Awaited<ReturnType<typeof getAwsInstanceTypes>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetAwsInstanceTypes<
  TData = Awaited<ReturnType<typeof getAwsInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  params?: GetAwsInstanceTypesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAwsInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get AWS instance types for a region
 */

export function useGetAwsInstanceTypes<
  TData = Awaited<ReturnType<typeof getAwsInstanceTypes>>,
  TError = ErrorType<ErrorModel>,
>(
  region: string,
  params?: GetAwsInstanceTypesParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAwsInstanceTypes>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetAwsInstanceTypesQueryOptions(
    region,
    params,
    options,
  );

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Assignments Logs
 */
export const getAssignmentLogs = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetUserAssignmentsLogsOutputBody>(
    { url: `/security/user-assignments-logs`, method: "GET", signal },
    options,
  );
};

export const getGetAssignmentLogsQueryKey = () => {
  return [`/security/user-assignments-logs`] as const;
};

export const getGetAssignmentLogsQueryOptions = <
  TData = Awaited<ReturnType<typeof getAssignmentLogs>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<
      Awaited<ReturnType<typeof getAssignmentLogs>>,
      TError,
      TData
    >
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetAssignmentLogsQueryKey();

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getAssignmentLogs>>
  > = ({ signal }) => getAssignmentLogs(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getAssignmentLogs>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetAssignmentLogsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getAssignmentLogs>>
>;
export type GetAssignmentLogsQueryError = ErrorType<ErrorModel>;

export function useGetAssignmentLogs<
  TData = Awaited<ReturnType<typeof getAssignmentLogs>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAssignmentLogs>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAssignmentLogs>>,
          TError,
          Awaited<ReturnType<typeof getAssignmentLogs>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetAssignmentLogs<
  TData = Awaited<ReturnType<typeof getAssignmentLogs>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAssignmentLogs>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getAssignmentLogs>>,
          TError,
          Awaited<ReturnType<typeof getAssignmentLogs>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetAssignmentLogs<
  TData = Awaited<ReturnType<typeof getAssignmentLogs>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAssignmentLogs>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Assignments Logs
 */

export function useGetAssignmentLogs<
  TData = Awaited<ReturnType<typeof getAssignmentLogs>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getAssignmentLogs>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetAssignmentLogsQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Users
 */
export const getUsers = (
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetUsersOutputBody>(
    { url: `/users`, method: "GET", signal },
    options,
  );
};

export const getGetUsersQueryKey = () => {
  return [`/users`] as const;
};

export const getGetUsersQueryOptions = <
  TData = Awaited<ReturnType<typeof getUsers>>,
  TError = ErrorType<ErrorModel>,
>(options?: {
  query?: Partial<
    UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>
  >;
  request?: SecondParameter<typeof customInstance>;
}) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUsersQueryKey();

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUsers>>> = ({
    signal,
  }) => getUsers(requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUsers>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUsersQueryResult = NonNullable<
  Awaited<ReturnType<typeof getUsers>>
>;
export type GetUsersQueryError = ErrorType<ErrorModel>;

export function useGetUsers<
  TData = Awaited<ReturnType<typeof getUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUsers>>,
          TError,
          Awaited<ReturnType<typeof getUsers>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUsers<
  TData = Awaited<ReturnType<typeof getUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUsers>>,
          TError,
          Awaited<ReturnType<typeof getUsers>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUsers<
  TData = Awaited<ReturnType<typeof getUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Users
 */

export function useGetUsers<
  TData = Awaited<ReturnType<typeof getUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUsers>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetUsersQueryOptions(options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get User Management Users
 */
export const getUserManagementUsers = (
  params?: GetUserManagementUsersParams,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetUserManagementOutputBody>(
    { url: `/users/user-management`, method: "GET", params, signal },
    options,
  );
};

export const getGetUserManagementUsersQueryKey = (
  params?: GetUserManagementUsersParams,
) => {
  return [`/users/user-management`, ...(params ? [params] : [])] as const;
};

export const getGetUserManagementUsersQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserManagementUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  params?: GetUserManagementUsersParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserManagementUsers>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetUserManagementUsersQueryKey(params);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getUserManagementUsers>>
  > = ({ signal }) => getUserManagementUsers(params, requestOptions, signal);

  return { queryKey, queryFn, ...queryOptions } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserManagementUsers>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserManagementUsersQueryResult = NonNullable<
  Awaited<ReturnType<typeof getUserManagementUsers>>
>;
export type GetUserManagementUsersQueryError = ErrorType<ErrorModel>;

export function useGetUserManagementUsers<
  TData = Awaited<ReturnType<typeof getUserManagementUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  params: undefined | GetUserManagementUsersParams,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserManagementUsers>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserManagementUsers>>,
          TError,
          Awaited<ReturnType<typeof getUserManagementUsers>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUserManagementUsers<
  TData = Awaited<ReturnType<typeof getUserManagementUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  params?: GetUserManagementUsersParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserManagementUsers>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserManagementUsers>>,
          TError,
          Awaited<ReturnType<typeof getUserManagementUsers>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUserManagementUsers<
  TData = Awaited<ReturnType<typeof getUserManagementUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  params?: GetUserManagementUsersParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserManagementUsers>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get User Management Users
 */

export function useGetUserManagementUsers<
  TData = Awaited<ReturnType<typeof getUserManagementUsers>>,
  TError = ErrorType<ErrorModel>,
>(
  params?: GetUserManagementUsersParams,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserManagementUsers>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetUserManagementUsersQueryOptions(params, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get User Details
 */
export const getUserDetails = (
  userId: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetUserDetailsOutputBody>(
    { url: `/users/${userId}`, method: "GET", signal },
    options,
  );
};

export const getGetUserDetailsQueryKey = (userId?: string) => {
  return [`/users/${userId}`] as const;
};

export const getGetUserDetailsQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserDetails>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserDetails>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserDetailsQueryKey(userId);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserDetails>>> = ({
    signal,
  }) => getUserDetails(userId, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!userId,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserDetails>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserDetailsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getUserDetails>>
>;
export type GetUserDetailsQueryError = ErrorType<ErrorModel>;

export function useGetUserDetails<
  TData = Awaited<ReturnType<typeof getUserDetails>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserDetails>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserDetails>>,
          TError,
          Awaited<ReturnType<typeof getUserDetails>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUserDetails<
  TData = Awaited<ReturnType<typeof getUserDetails>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserDetails>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserDetails>>,
          TError,
          Awaited<ReturnType<typeof getUserDetails>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUserDetails<
  TData = Awaited<ReturnType<typeof getUserDetails>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserDetails>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get User Details
 */

export function useGetUserDetails<
  TData = Awaited<ReturnType<typeof getUserDetails>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserDetails>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetUserDetailsQueryOptions(userId, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Edit User username
 */
export const editUserUsername = (
  userId: string,
  editUserUsernameInputBody: BodyType<NonReadonly<EditUserUsernameInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    {
      url: `/users/${userId}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editUserUsernameInputBody,
    },
    options,
  );
};

export const getEditUserUsernameMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editUserUsername>>,
    TError,
    { userId: string; data: BodyType<NonReadonly<EditUserUsernameInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editUserUsername>>,
  TError,
  { userId: string; data: BodyType<NonReadonly<EditUserUsernameInputBody>> },
  TContext
> => {
  const mutationKey = ["editUserUsername"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editUserUsername>>,
    { userId: string; data: BodyType<NonReadonly<EditUserUsernameInputBody>> }
  > = (props) => {
    const { userId, data } = props ?? {};

    return editUserUsername(userId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditUserUsernameMutationResult = NonNullable<
  Awaited<ReturnType<typeof editUserUsername>>
>;
export type EditUserUsernameMutationBody = BodyType<
  NonReadonly<EditUserUsernameInputBody>
>;
export type EditUserUsernameMutationError = ErrorType<ErrorModel>;

/**
 * @summary Edit User username
 */
export const useEditUserUsername = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editUserUsername>>,
      TError,
      {
        userId: string;
        data: BodyType<NonReadonly<EditUserUsernameInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editUserUsername>>,
  TError,
  { userId: string; data: BodyType<NonReadonly<EditUserUsernameInputBody>> },
  TContext
> => {
  const mutationOptions = getEditUserUsernameMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Get User Engagement Titles
 */
export const getUserEngagementTitles = (
  userId: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetUserEngagementTitlesOutputBody>(
    { url: `/users/${userId}/engagements`, method: "GET", signal },
    options,
  );
};

export const getGetUserEngagementTitlesQueryKey = (userId?: string) => {
  return [`/users/${userId}/engagements`] as const;
};

export const getGetUserEngagementTitlesQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserEngagementTitles>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserEngagementTitles>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey =
    queryOptions?.queryKey ?? getGetUserEngagementTitlesQueryKey(userId);

  const queryFn: QueryFunction<
    Awaited<ReturnType<typeof getUserEngagementTitles>>
  > = ({ signal }) => getUserEngagementTitles(userId, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!userId,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserEngagementTitles>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserEngagementTitlesQueryResult = NonNullable<
  Awaited<ReturnType<typeof getUserEngagementTitles>>
>;
export type GetUserEngagementTitlesQueryError = ErrorType<ErrorModel>;

export function useGetUserEngagementTitles<
  TData = Awaited<ReturnType<typeof getUserEngagementTitles>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options: {
    query: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserEngagementTitles>>,
        TError,
        TData
      >
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserEngagementTitles>>,
          TError,
          Awaited<ReturnType<typeof getUserEngagementTitles>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUserEngagementTitles<
  TData = Awaited<ReturnType<typeof getUserEngagementTitles>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserEngagementTitles>>,
        TError,
        TData
      >
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserEngagementTitles>>,
          TError,
          Awaited<ReturnType<typeof getUserEngagementTitles>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUserEngagementTitles<
  TData = Awaited<ReturnType<typeof getUserEngagementTitles>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserEngagementTitles>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get User Engagement Titles
 */

export function useGetUserEngagementTitles<
  TData = Awaited<ReturnType<typeof getUserEngagementTitles>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<
        Awaited<ReturnType<typeof getUserEngagementTitles>>,
        TError,
        TData
      >
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetUserEngagementTitlesQueryOptions(userId, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Get Scripts
 */
export const getUserScripts = (
  userId: string,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<GetScriptsOutputBody>(
    { url: `/users/${userId}/scripts`, method: "GET", signal },
    options,
  );
};

export const getGetUserScriptsQueryKey = (userId?: string) => {
  return [`/users/${userId}/scripts`] as const;
};

export const getGetUserScriptsQueryOptions = <
  TData = Awaited<ReturnType<typeof getUserScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserScripts>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
) => {
  const { query: queryOptions, request: requestOptions } = options ?? {};

  const queryKey = queryOptions?.queryKey ?? getGetUserScriptsQueryKey(userId);

  const queryFn: QueryFunction<Awaited<ReturnType<typeof getUserScripts>>> = ({
    signal,
  }) => getUserScripts(userId, requestOptions, signal);

  return {
    queryKey,
    queryFn,
    enabled: !!userId,
    ...queryOptions,
  } as UseQueryOptions<
    Awaited<ReturnType<typeof getUserScripts>>,
    TError,
    TData
  > & { queryKey: DataTag<QueryKey, TData, TError> };
};

export type GetUserScriptsQueryResult = NonNullable<
  Awaited<ReturnType<typeof getUserScripts>>
>;
export type GetUserScriptsQueryError = ErrorType<ErrorModel>;

export function useGetUserScripts<
  TData = Awaited<ReturnType<typeof getUserScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options: {
    query: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserScripts>>, TError, TData>
    > &
      Pick<
        DefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserScripts>>,
          TError,
          Awaited<ReturnType<typeof getUserScripts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): DefinedUseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUserScripts<
  TData = Awaited<ReturnType<typeof getUserScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserScripts>>, TError, TData>
    > &
      Pick<
        UndefinedInitialDataOptions<
          Awaited<ReturnType<typeof getUserScripts>>,
          TError,
          Awaited<ReturnType<typeof getUserScripts>>
        >,
        "initialData"
      >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
export function useGetUserScripts<
  TData = Awaited<ReturnType<typeof getUserScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserScripts>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
};
/**
 * @summary Get Scripts
 */

export function useGetUserScripts<
  TData = Awaited<ReturnType<typeof getUserScripts>>,
  TError = ErrorType<ErrorModel>,
>(
  userId: string,
  options?: {
    query?: Partial<
      UseQueryOptions<Awaited<ReturnType<typeof getUserScripts>>, TError, TData>
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseQueryResult<TData, TError> & {
  queryKey: DataTag<QueryKey, TData, TError>;
} {
  const queryOptions = getGetUserScriptsQueryOptions(userId, options);

  const query = useQuery(queryOptions, queryClient) as UseQueryResult<
    TData,
    TError
  > & { queryKey: DataTag<QueryKey, TData, TError> };

  query.queryKey = queryOptions.queryKey;

  return query;
}

/**
 * @summary Create Script
 */
export const postUserScript = (
  userId: string,
  createScriptInputBody: BodyType<NonReadonly<CreateScriptInputBody>>,
  options?: SecondParameter<typeof customInstance>,
  signal?: AbortSignal,
) => {
  return customInstance<CreateScriptOutputBody>(
    {
      url: `/users/${userId}/scripts`,
      method: "POST",
      headers: { "Content-Type": "application/json" },
      data: createScriptInputBody,
      signal,
    },
    options,
  );
};

export const getPostUserScriptMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof postUserScript>>,
    TError,
    { userId: string; data: BodyType<NonReadonly<CreateScriptInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof postUserScript>>,
  TError,
  { userId: string; data: BodyType<NonReadonly<CreateScriptInputBody>> },
  TContext
> => {
  const mutationKey = ["postUserScript"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof postUserScript>>,
    { userId: string; data: BodyType<NonReadonly<CreateScriptInputBody>> }
  > = (props) => {
    const { userId, data } = props ?? {};

    return postUserScript(userId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type PostUserScriptMutationResult = NonNullable<
  Awaited<ReturnType<typeof postUserScript>>
>;
export type PostUserScriptMutationBody = BodyType<
  NonReadonly<CreateScriptInputBody>
>;
export type PostUserScriptMutationError = ErrorType<ErrorModel>;

/**
 * @summary Create Script
 */
export const usePostUserScript = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof postUserScript>>,
      TError,
      { userId: string; data: BodyType<NonReadonly<CreateScriptInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof postUserScript>>,
  TError,
  { userId: string; data: BodyType<NonReadonly<CreateScriptInputBody>> },
  TContext
> => {
  const mutationOptions = getPostUserScriptMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Delete Script
 */
export const deleteUserScript = (
  userId: string,
  scriptId: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    { url: `/users/${userId}/scripts/${scriptId}`, method: "DELETE" },
    options,
  );
};

export const getDeleteUserScriptMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteUserScript>>,
    TError,
    { userId: string; scriptId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteUserScript>>,
  TError,
  { userId: string; scriptId: string },
  TContext
> => {
  const mutationKey = ["deleteUserScript"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteUserScript>>,
    { userId: string; scriptId: string }
  > = (props) => {
    const { userId, scriptId } = props ?? {};

    return deleteUserScript(userId, scriptId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteUserScriptMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteUserScript>>
>;

export type DeleteUserScriptMutationError = ErrorType<ErrorModel>;

/**
 * @summary Delete Script
 */
export const useDeleteUserScript = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof deleteUserScript>>,
      TError,
      { userId: string; scriptId: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof deleteUserScript>>,
  TError,
  { userId: string; scriptId: string },
  TContext
> => {
  const mutationOptions = getDeleteUserScriptMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Edit Script
 */
export const editUserScript = (
  userId: string,
  scriptId: string,
  editScriptInputBody: BodyType<NonReadonly<EditScriptInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<EditScriptOutputBody>(
    {
      url: `/users/${userId}/scripts/${scriptId}`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: editScriptInputBody,
    },
    options,
  );
};

export const getEditUserScriptMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof editUserScript>>,
    TError,
    {
      userId: string;
      scriptId: string;
      data: BodyType<NonReadonly<EditScriptInputBody>>;
    },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof editUserScript>>,
  TError,
  {
    userId: string;
    scriptId: string;
    data: BodyType<NonReadonly<EditScriptInputBody>>;
  },
  TContext
> => {
  const mutationKey = ["editUserScript"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof editUserScript>>,
    {
      userId: string;
      scriptId: string;
      data: BodyType<NonReadonly<EditScriptInputBody>>;
    }
  > = (props) => {
    const { userId, scriptId, data } = props ?? {};

    return editUserScript(userId, scriptId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type EditUserScriptMutationResult = NonNullable<
  Awaited<ReturnType<typeof editUserScript>>
>;
export type EditUserScriptMutationBody = BodyType<
  NonReadonly<EditScriptInputBody>
>;
export type EditUserScriptMutationError = ErrorType<ErrorModel>;

/**
 * @summary Edit Script
 */
export const useEditUserScript = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof editUserScript>>,
      TError,
      {
        userId: string;
        scriptId: string;
        data: BodyType<NonReadonly<EditScriptInputBody>>;
      },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof editUserScript>>,
  TError,
  {
    userId: string;
    scriptId: string;
    data: BodyType<NonReadonly<EditScriptInputBody>>;
  },
  TContext
> => {
  const mutationOptions = getEditUserScriptMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Delete User SSH Key
 */
export const deleteUserSshKey = (
  userId: string,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<null>(
    { url: `/users/${userId}/ssh-key`, method: "DELETE" },
    options,
  );
};

export const getDeleteUserSshKeyMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof deleteUserSshKey>>,
    TError,
    { userId: string },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof deleteUserSshKey>>,
  TError,
  { userId: string },
  TContext
> => {
  const mutationKey = ["deleteUserSshKey"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof deleteUserSshKey>>,
    { userId: string }
  > = (props) => {
    const { userId } = props ?? {};

    return deleteUserSshKey(userId, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type DeleteUserSshKeyMutationResult = NonNullable<
  Awaited<ReturnType<typeof deleteUserSshKey>>
>;

export type DeleteUserSshKeyMutationError = ErrorType<ErrorModel>;

/**
 * @summary Delete User SSH Key
 */
export const useDeleteUserSshKey = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof deleteUserSshKey>>,
      TError,
      { userId: string },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof deleteUserSshKey>>,
  TError,
  { userId: string },
  TContext
> => {
  const mutationOptions = getDeleteUserSshKeyMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};

/**
 * @summary Set User SSH Key
 */
export const setUserSshKey = (
  userId: string,
  setSshKeyInputBody: BodyType<NonReadonly<SetSshKeyInputBody>>,
  options?: SecondParameter<typeof customInstance>,
) => {
  return customInstance<SetSshKeyOutputBody>(
    {
      url: `/users/${userId}/ssh-key`,
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      data: setSshKeyInputBody,
    },
    options,
  );
};

export const getSetUserSshKeyMutationOptions = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(options?: {
  mutation?: UseMutationOptions<
    Awaited<ReturnType<typeof setUserSshKey>>,
    TError,
    { userId: string; data: BodyType<NonReadonly<SetSshKeyInputBody>> },
    TContext
  >;
  request?: SecondParameter<typeof customInstance>;
}): UseMutationOptions<
  Awaited<ReturnType<typeof setUserSshKey>>,
  TError,
  { userId: string; data: BodyType<NonReadonly<SetSshKeyInputBody>> },
  TContext
> => {
  const mutationKey = ["setUserSshKey"];
  const { mutation: mutationOptions, request: requestOptions } = options
    ? options.mutation &&
      "mutationKey" in options.mutation &&
      options.mutation.mutationKey
      ? options
      : { ...options, mutation: { ...options.mutation, mutationKey } }
    : { mutation: { mutationKey }, request: undefined };

  const mutationFn: MutationFunction<
    Awaited<ReturnType<typeof setUserSshKey>>,
    { userId: string; data: BodyType<NonReadonly<SetSshKeyInputBody>> }
  > = (props) => {
    const { userId, data } = props ?? {};

    return setUserSshKey(userId, data, requestOptions);
  };

  return { mutationFn, ...mutationOptions };
};

export type SetUserSshKeyMutationResult = NonNullable<
  Awaited<ReturnType<typeof setUserSshKey>>
>;
export type SetUserSshKeyMutationBody = BodyType<
  NonReadonly<SetSshKeyInputBody>
>;
export type SetUserSshKeyMutationError = ErrorType<ErrorModel>;

/**
 * @summary Set User SSH Key
 */
export const useSetUserSshKey = <
  TError = ErrorType<ErrorModel>,
  TContext = unknown,
>(
  options?: {
    mutation?: UseMutationOptions<
      Awaited<ReturnType<typeof setUserSshKey>>,
      TError,
      { userId: string; data: BodyType<NonReadonly<SetSshKeyInputBody>> },
      TContext
    >;
    request?: SecondParameter<typeof customInstance>;
  },
  queryClient?: QueryClient,
): UseMutationResult<
  Awaited<ReturnType<typeof setUserSshKey>>,
  TError,
  { userId: string; data: BodyType<NonReadonly<SetSshKeyInputBody>> },
  TContext
> => {
  const mutationOptions = getSetUserSshKeyMutationOptions(options);

  return useMutation(mutationOptions, queryClient);
};
