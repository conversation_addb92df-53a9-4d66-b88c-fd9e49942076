{"name": "engage-ui-v2", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": "^22", "pnpm": "^10"}, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite", "build": "tsc -b && vite build", "prod": "cross-env NODE_ENV=production vite", "lint": "eslint .", "preview": "vite preview", "orval": "orval", "format": "prettier . --write"}, "dependencies": {"@azure/msal-browser": "4.21.1", "@azure/msal-react": "3.0.19", "@fortawesome/fontawesome-svg-core": "7.0.1", "@fortawesome/free-solid-svg-icons": "7.0.1", "@fortawesome/react-fontawesome": "3.0.2", "@headlessui/react": "^2.2.7", "@popperjs/core": "^2.11.8", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-query": "^5.85.9", "@tanstack/react-query-devtools": "^5.85.9", "@tanstack/react-router": "^1.131.32", "@tanstack/react-table": "^8.21.3", "@tippyjs/react": "^4.2.6", "axios": "^1.11.0", "clsx": "^2.1.1", "cytoscape": "^3.33.1", "cytoscape-context-menus": "^4.2.1", "cytoscape-dagre": "^2.5.0", "cytoscape-popper": "^4.0.1", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "highlight.js": "^11.11.1", "primereact": "^10.9.7", "react": "^19.1.1", "react-cytoscapejs": "^2.0.0", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "tippy.js": "^6.3.7", "zod": "^4.1.5"}, "devDependencies": {"@eslint/js": "^9.34.0", "@tailwindcss/postcss": "^4.1.12", "@tanstack/eslint-plugin-query": "^5.83.1", "@tanstack/router-devtools": "^1.131.32", "@tanstack/router-vite-plugin": "^1.131.32", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/cytoscape-context-menus": "^4.1.4", "@types/cytoscape-dagre": "^2.3.3", "@types/cytoscape-popper": "^2.0.4", "@types/node": "^24.3.0", "@types/react": "^19.1.12", "@types/react-cytoscapejs": "^1.2.5", "@types/react-dom": "^19.1.9", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "@vitejs/plugin-react": "^5.0.2", "cross-env": "^10.0.0", "eslint": "^9.34.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "orval": "^7.11.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.12", "typescript": "^5.9.2", "typescript-eslint": "^8.42.0", "vite": "^7.1.4", "vite-plugin-svgr": "^4.5.0"}, "prettier": {"plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^@core/(.*)$", "^@server/(.*)$", "^@ui/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}, "pnpm": {"overrides": {"esbuild@<=0.24.2": ">=0.25.0"}, "onlyBuiltDependencies": ["esbuild", "@tailwindcss/oxide"]}}